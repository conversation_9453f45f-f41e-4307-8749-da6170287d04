# Open Search serverless.
# 1. Data access policy for Open Search Collection

resource "aws_opensearchserverless_access_policy" "pg-data-access-policy" {
  name        = "pg-data-access-policy"
  type        = "data"
  description = "read and write permissions"
  policy = jsonencode([
    {
      Rules = [
        {
          ResourceType = "index",
          Resource = [
          "index/pg-*/*"
        ],
          Permission = [
          "aoss:CreateIndex",
          "aoss:DeleteIndex",
          "aoss:UpdateIndex",
          "aoss:DescribeIndex",
          "aoss:ReadDocument",
          "aoss:WriteDocument"
        ]
        },
        {
          ResourceType = "collection",
          Resource = [
          "collection/pg-*"
        ],
          Permission = [
          "aoss:CreateCollectionItems",
          "aoss:DeleteCollectionItems",
          "aoss:UpdateCollectionItems",
          "aoss:DescribeCollectionItems"
        ]
        }
      ],
      Principal = var.data_access_policy_principals
    }
  ])
}

#2.Encryption Policy.

resource "aws_opensearchserverless_security_policy" "security-policy" {
  name        = "security-policy"
  type        = "encryption"
  description = "encryption security policy for example-collection"
  policy = jsonencode({
Rules = [
        {
            "ResourceType": "collection",
            "Resource": [
                "collection/pg-*"
            ]
        }
        ],
    AWSOwnedKey = true
  })
}

#3. Network Policy.

resource "aws_opensearchserverless_security_policy" "pg-network-policy" {
  name        = "pg-network-policy"
  type        = "network"
  description = "Public access"
  policy = jsonencode([
    {
      Description = "Public access to collection and Dashboards endpoint for example collection",
      Rules = [
            {
                "Resource": [
                "collection/pg-*"
                ],
                "ResourceType": "dashboard"
            },
            {
                "Resource": [
                "collection/pg-*"
                ],
                "ResourceType": "collection"
            }
            ],
      AllowFromPublic = true
    }
  ])
}

#4. Collection.

resource "aws_opensearchserverless_collection" "pg-collection-opensearch" {
  name = "pg-collection-opensearch"
  depends_on = [aws_opensearchserverless_security_policy.security-policy]
}
