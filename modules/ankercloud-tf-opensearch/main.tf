# Local variables for naming and resource patterns
locals {
  collection_name         = var.collection_name != null ? var.collection_name : "${var.name_prefix}-${var.environment}-collection"
  data_access_policy_name = var.data_access_policy_name != null ? var.data_access_policy_name : "${var.name_prefix}-${var.environment}-data-policy"
  encryption_policy_name  = var.encryption_policy_name != null ? var.encryption_policy_name : "${var.name_prefix}-${var.environment}-enc-policy"
  network_policy_name     = var.network_policy_name != null ? var.network_policy_name : "${var.name_prefix}-${var.environment}-net-policy"
  resource_pattern        = var.resource_pattern != null ? var.resource_pattern : "${var.name_prefix}-*"
}

# Data Access Policy for OpenSearch Serverless Collection
resource "aws_opensearchserverless_access_policy" "data_access_policy" {
  name        = local.data_access_policy_name
  type        = "data"
  description = "Data access policy for ${local.collection_name} collection"

  policy = jsonencode([
    {
      Rules = [
        {
          ResourceType = "index",
          Resource = [
            "index/${local.resource_pattern}/*"
          ],
          Permission = [
            "aoss:CreateIndex",
            "aoss:DeleteIndex",
            "aoss:UpdateIndex",
            "aoss:DescribeIndex",
            "aoss:ReadDocument",
            "aoss:WriteDocument"
          ]
        },
        {
          ResourceType = "collection",
          Resource = [
            "collection/${local.resource_pattern}"
          ],
          Permission = [
            "aoss:CreateCollectionItems",
            "aoss:DeleteCollectionItems",
            "aoss:UpdateCollectionItems",
            "aoss:DescribeCollectionItems"
          ]
        }
      ],
      Principal = var.data_access_policy_principals
    }
  ])
}

# Encryption Security Policy
resource "aws_opensearchserverless_security_policy" "encryption_policy" {
  name        = local.encryption_policy_name
  type        = "encryption"
  description = "Encryption security policy for ${local.collection_name} collection"

  policy = jsonencode({
    Rules = [
      {
        ResourceType = "collection",
        Resource = [
          "collection/${local.resource_pattern}"
        ]
      }
    ],
    AWSOwnedKey = true
  })
}

# Network Security Policy
resource "aws_opensearchserverless_security_policy" "network_policy" {
  name        = local.network_policy_name
  type        = "network"
  description = "${var.network_access_type} access policy for ${local.collection_name} collection"

  policy = jsonencode([
    {
      Description = "${var.network_access_type} access to collection and Dashboards endpoint",
      Rules = [
        {
          Resource = [
            "collection/${local.resource_pattern}"
          ],
          ResourceType = "dashboard"
        },
        {
          Resource = [
            "collection/${local.resource_pattern}"
          ],
          ResourceType = "collection"
        }
      ],
      AllowFromPublic = var.network_access_type == "Public" ? true : null,
      SourceVPCEs     = var.network_access_type == "VPC" ? var.vpc_endpoints : null
    }
  ])
}

# OpenSearch Serverless Collection
resource "aws_opensearchserverless_collection" "collection" {
  name        = local.collection_name
  type        = var.collection_type
  description = var.collection_description

  depends_on = [
    aws_opensearchserverless_security_policy.encryption_policy,
    aws_opensearchserverless_security_policy.network_policy
  ]

  tags = merge(var.tags, {
    Name = local.collection_name
  })
}
