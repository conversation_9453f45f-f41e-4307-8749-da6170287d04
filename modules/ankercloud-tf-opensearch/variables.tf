# Naming and Environment
variable "name_prefix" {
  type        = string
  description = "Prefix for naming OpenSearch resources"
  default     = "ankercloud"
}

variable "environment" {
  type        = string
  description = "Environment name (e.g., dev, staging, prod)"
  default     = "dev"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all OpenSearch resources"
  default     = {}
}

# Collection Configuration
variable "collection_name" {
  type        = string
  description = "Name of the OpenSearch Serverless collection"
  default     = null
}

variable "collection_description" {
  type        = string
  description = "Description of the OpenSearch Serverless collection"
  default     = "AnkerCloud OpenSearch Serverless collection"
}

variable "collection_type" {
  type        = string
  description = "Type of the OpenSearch Serverless collection"
  default     = "SEARCH"
  validation {
    condition     = contains(["SEARCH", "TIMESERIES"], var.collection_type)
    error_message = "Collection type must be either SEARCH or TIMESERIES."
  }
}

# Access Policy Configuration
variable "data_access_policy_principals" {
  type        = list(string)
  description = "List of IAM principal ARNs that should have data access to the OpenSearch collection"
}

variable "data_access_policy_name" {
  type        = string
  description = "Name for the data access policy"
  default     = null
}

# Security Policy Configuration
variable "encryption_policy_name" {
  type        = string
  description = "Name for the encryption security policy"
  default     = null
}

variable "network_policy_name" {
  type        = string
  description = "Name for the network security policy"
  default     = null
}

variable "network_access_type" {
  type        = string
  description = "Network access type for the collection (Public or VPC)"
  default     = "Public"
  validation {
    condition     = contains(["Public", "VPC"], var.network_access_type)
    error_message = "Network access type must be either Public or VPC."
  }
}

variable "vpc_endpoints" {
  type        = list(string)
  description = "List of VPC endpoint IDs for VPC access (required if network_access_type is VPC)"
  default     = []
}

# Resource Pattern Configuration
variable "resource_pattern" {
  type        = string
  description = "Resource pattern for policies (e.g., 'pg-*' for resources starting with 'pg-')"
  default     = null
}
