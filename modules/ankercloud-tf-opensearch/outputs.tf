
output "collection_arn" {
  description = "ARN of the OpenSearch Serverless collection"
  value       = aws_opensearchserverless_collection.collection.arn
}

output "collection_id" {
  description = "ID of the OpenSearch Serverless collection"
  value       = aws_opensearchserverless_collection.collection.id
}

output "collection_name" {
  description = "Name of the OpenSearch Serverless collection"
  value       = aws_opensearchserverless_collection.collection.name
}

output "collection_endpoint" {
  description = "Collection endpoint for the OpenSearch Serverless collection"
  value       = aws_opensearchserverless_collection.collection.collection_endpoint
}

output "dashboard_endpoint" {
  description = "Dashboard endpoint for the OpenSearch Serverless collection"
  value       = aws_opensearchserverless_collection.collection.dashboard_endpoint
}

output "data_access_policy_name" {
  description = "Name of the data access policy"
  value       = aws_opensearchserverless_access_policy.data_access_policy.name
}

output "encryption_policy_name" {
  description = "Name of the encryption security policy"
  value       = aws_opensearchserverless_security_policy.encryption_policy.name
}

output "network_policy_name" {
  description = "Name of the network security policy"
  value       = aws_opensearchserverless_security_policy.network_policy.name
}

output "collection_details" {
  description = "Complete details of the OpenSearch Serverless collection"
  value = {
    arn                 = aws_opensearchserverless_collection.collection.arn
    id                  = aws_opensearchserverless_collection.collection.id
    name                = aws_opensearchserverless_collection.collection.name
    type                = aws_opensearchserverless_collection.collection.type
    collection_endpoint = aws_opensearchserverless_collection.collection.collection_endpoint
    dashboard_endpoint  = aws_opensearchserverless_collection.collection.dashboard_endpoint
  }
}
