# AnkerCloud Terraform OpenSearch Serverless Module

This Terraform module creates AWS OpenSearch Serverless resources including collections, access policies, and security policies for AnkerCloud applications.

## Features

- OpenSearch Serverless collection with configurable type (SEARCH or TIMESERIES)
- Data access policy with configurable permissions
- Encryption security policy with AWS-owned keys
- Network security policy supporting both Public and VPC access
- Configurable resource patterns for flexible policy management
- Comprehensive tagging support

## Usage

### Basic Usage (Public Access)

```hcl
module "ankercloud_opensearch" {
  source = "../modules/ankercloud-tf-opensearch"

  # Naming and Environment
  name_prefix = "ankercloud"
  environment = "dev"

  # Access Configuration
  data_access_policy_principals = [
    "arn:aws:iam::123456789012:role/lambda-execution-role",
    "arn:aws:iam::123456789012:user/admin-user"
  ]

  # Collection Configuration
  collection_type        = "SEARCH"
  collection_description = "AnkerCloud search collection for dev environment"

  # Network Access
  network_access_type = "Public"

  # Tags
  tags = {
    Environment = "dev"
    Project     = "AnkerCloud"
  }
}
```

### VPC Access Configuration

```hcl
module "ankercloud_opensearch" {
  source = "../modules/ankercloud-tf-opensearch"

  name_prefix   = "ankercloud"
  environment   = "prod"

  data_access_policy_principals = [
    "arn:aws:iam::123456789012:role/lambda-execution-role"
  ]

  # VPC Configuration
  network_access_type = "VPC"
  vpc_endpoints       = ["vpce-12345678"]

  tags = {
    Environment = "prod"
    Project     = "AnkerCloud"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version  |
| ------------------------------------------------------ | -------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0.0 |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                           | Type     |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------- |
| [aws_opensearchserverless_access_policy.data_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/opensearchserverless_access_policy)    | resource |
| [aws_opensearchserverless_collection.collection](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/opensearchserverless_collection)                  | resource |
| [aws_opensearchserverless_security_policy.encryption_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/opensearchserverless_security_policy) | resource |
| [aws_opensearchserverless_security_policy.network_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/opensearchserverless_security_policy)    | resource |

## Inputs

| Name                                                                                                                     | Description                                                                          | Type           | Default                                         | Required |
| ------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------ | -------------- | ----------------------------------------------- | :------: |
| <a name="input_collection_description"></a> [collection_description](#input_collection_description)                      | Description of the OpenSearch Serverless collection                                  | `string`       | `"AnkerCloud OpenSearch Serverless collection"` |    no    |
| <a name="input_collection_name"></a> [collection_name](#input_collection_name)                                           | Name of the OpenSearch Serverless collection                                         | `string`       | `null`                                          |    no    |
| <a name="input_collection_type"></a> [collection_type](#input_collection_type)                                           | Type of the OpenSearch Serverless collection                                         | `string`       | `"SEARCH"`                                      |    no    |
| <a name="input_data_access_policy_name"></a> [data_access_policy_name](#input_data_access_policy_name)                   | Name for the data access policy                                                      | `string`       | `null`                                          |    no    |
| <a name="input_data_access_policy_principals"></a> [data_access_policy_principals](#input_data_access_policy_principals) | List of IAM principal ARNs that should have data access to the OpenSearch collection | `list(string)` | n/a                                             |   yes    |
| <a name="input_encryption_policy_name"></a> [encryption_policy_name](#input_encryption_policy_name)                      | Name for the encryption security policy                                              | `string`       | `null`                                          |    no    |
| <a name="input_environment"></a> [environment](#input_environment)                                                       | Environment name (e.g., dev, staging, prod)                                          | `string`       | `"dev"`                                         |    no    |
| <a name="input_name_prefix"></a> [name_prefix](#input_name_prefix)                                                       | Prefix for naming OpenSearch resources                                               | `string`       | `"ankercloud"`                                  |    no    |
| <a name="input_network_access_type"></a> [network_access_type](#input_network_access_type)                               | Network access type for the collection (Public or VPC)                               | `string`       | `"Public"`                                      |    no    |
| <a name="input_network_policy_name"></a> [network_policy_name](#input_network_policy_name)                               | Name for the network security policy                                                 | `string`       | `null`                                          |    no    |
| <a name="input_resource_pattern"></a> [resource_pattern](#input_resource_pattern)                                        | Resource pattern for policies (e.g., 'pg-\*' for resources starting with 'pg-')      | `string`       | `null`                                          |    no    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                            | Tags to apply to all OpenSearch resources                                            | `map(string)`  | `{}`                                            |    no    |
| <a name="input_vpc_endpoints"></a> [vpc_endpoints](#input_vpc_endpoints)                                                 | List of VPC endpoint IDs for VPC access (required if network_access_type is VPC)     | `list(string)` | `[]`                                            |    no    |

## Outputs

| Name                                                                                                     | Description                                                  |
| -------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------ |
| <a name="output_collection_arn"></a> [collection_arn](#output_collection_arn)                            | ARN of the OpenSearch Serverless collection                  |
| <a name="output_collection_details"></a> [collection_details](#output_collection_details)                | Complete details of the OpenSearch Serverless collection     |
| <a name="output_collection_endpoint"></a> [collection_endpoint](#output_collection_endpoint)             | Collection endpoint for the OpenSearch Serverless collection |
| <a name="output_collection_id"></a> [collection_id](#output_collection_id)                               | ID of the OpenSearch Serverless collection                   |
| <a name="output_collection_name"></a> [collection_name](#output_collection_name)                         | Name of the OpenSearch Serverless collection                 |
| <a name="output_dashboard_endpoint"></a> [dashboard_endpoint](#output_dashboard_endpoint)                | Dashboard endpoint for the OpenSearch Serverless collection  |
| <a name="output_data_access_policy_name"></a> [data_access_policy_name](#output_data_access_policy_name) | Name of the data access policy                               |
| <a name="output_encryption_policy_name"></a> [encryption_policy_name](#output_encryption_policy_name)    | Name of the encryption security policy                       |
| <a name="output_network_policy_name"></a> [network_policy_name](#output_network_policy_name)             | Name of the network security policy                          |

<!-- END_TF_DOCS -->
