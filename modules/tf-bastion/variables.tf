variable "bastion_users" {
  type        = map(string)
  description = "Users and their SSH pub keys"
}

variable "name" {
  type        = string
  description = "The name of the instance to be created"
}

variable "tag_map" {
  type        = map(string)
  description = "Tags to apply to resources"
}

variable "subnet_id" {
  type        = string
  description = "Which public subnet should this be in"
}

variable "vpc_id" {
  type        = string
  description = "ID of the VPC"
}

variable "instance_type" {
  type        = string
  description = "The instance type to use, must be graviton (arm64)"
  default     = "t4g.micro"
}
