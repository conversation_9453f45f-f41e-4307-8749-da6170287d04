data "aws_ami" "ubuntu24" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_iam_policy_document" "this" {
  statement {
    sid       = ""
    effect    = "Allow"
    resources = ["arn:aws:logs:*:*:*"]

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams",
    ]
  }
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "this" {
  name               = "Bastion-Role-${var.name}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
  tags               = var.tag_map
}

resource "aws_iam_role_policy" "this_logs_policy" {
  name   = "${var.name}-Logs-Policy"
  role   = aws_iam_role.this.name
  policy = data.aws_iam_policy_document.this.json
}

resource "aws_iam_role_policy_attachment" "ssm_policy" {
  role       = aws_iam_role.this.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_instance_profile" "this" {
  name = "EC2SSMInstanceProfile-${var.name}"
  role = aws_iam_role.this.name
  tags = var.tag_map
}

resource "aws_instance" "this" {
  ami                         = data.aws_ami.ubuntu24.id
  associate_public_ip_address = true
  iam_instance_profile        = aws_iam_instance_profile.this.name
  instance_type               = var.instance_type
  subnet_id                   = var.subnet_id
  user_data_base64            = data.cloudinit_config.this.rendered
  user_data_replace_on_change = true
  vpc_security_group_ids      = [aws_security_group.bastion_sg.id]

  root_block_device {
    volume_size           = 20
    volume_type           = "gp3"
    delete_on_termination = true
    encrypted             = true
  }
  tags = merge(var.tag_map, { "Name" = "Bastion-${var.name}" })
}



data "cloudinit_config" "this" {
  gzip          = true
  base64_encode = true

  part {
    content_type = "text/cloud-config"
    content = templatefile(
      "${path.module}/cloud-config.yml.tftpl",
      { bastion_users : var.bastion_users }
    )
  }
}

resource "aws_security_group" "bastion_sg" {
  name        = "Bastion-SG-${var.name}"
  description = "Security group for the Bastion host ${var.name}"
  vpc_id      = var.vpc_id
  tags        = var.tag_map
}

resource "aws_security_group_rule" "ssh_ingress" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.bastion_sg.id
  description       = "Allow SSH from anywhere"
}

resource "aws_security_group_rule" "all_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.bastion_sg.id
  description       = "Allow all outbound traffic"
}
