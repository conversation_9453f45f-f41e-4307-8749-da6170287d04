output "bastion_public_ip" {
  description = "The public IP address of the Bastion instance"
  value       = aws_instance.this.public_ip
}

output "bastion_public_dns" {
  description = "The public DNS name of the Bastion instance"
  value       = aws_instance.this.public_dns
}

output "bastion_private_ip" {
  description = "The private IP address of the Bastion instance"
  value       = aws_instance.this.private_ip
}

output "bastion_private_dns" {
  description = "The private DNS name of the Bastion instance"
  value       = aws_instance.this.private_dns
}

output "bastion_security_group_arn" {
  description = "The security group ARN of the Bastion instance"
  value       = aws_security_group.bastion_sg.arn
}

output "bastion_security_group_id" {
  description = "The security group ID of the Bastion instance"
  value       = aws_security_group.bastion_sg.id
}

output "bastion_security_group_name" {
  description = "The name of the security group for the Bastion instance"
  value       = aws_security_group.bastion_sg.name
}
