# tf-bastion

Provisions an SSH bastion host in a public subnet with user-defined SSH key configurations for secure access to a VPC

## Example

```terraform
module "bastion" {
  source = "./modules/bastion"

  name         = "foobar-bastion"
  subnet_id    = module.vpc.subnets.public[0].id
  vpc_id       = module.vpc.vpc.id
  tag_map      = {
    Environment = "dev"
    Project     = "some-project"
  }

  bastion_users = {
    mike = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDexamplekey1"
    alice = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDexamplekey2"
  }
}

```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                                     | Version |
| ------------------------------------------------------------------------ | ------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws)                   | >= 5.0  |
| <a name="requirement_cloudinit"></a> [cloudinit](#requirement_cloudinit) | >= 2.3  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                | Type        |
| --------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_iam_instance_profile.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile)                   | resource    |
| [aws_iam_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                           | resource    |
| [aws_iam_role_policy.this_logs_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy)                 | resource    |
| [aws_iam_role_policy_attachment.ssm_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource    |
| [aws_instance.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/instance)                                           | resource    |
| [aws_security_group.bastion_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)                         | resource    |
| [aws_security_group_rule.all_egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)               | resource    |
| [aws_security_group_rule.ssh_ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)              | resource    |
| [aws_ami.ubuntu24](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami)                                              | data source |
| [aws_iam_policy_document.assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)    | data source |
| [aws_iam_policy_document.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                  | data source |
| [cloudinit_config.this](https://registry.terraform.io/providers/hashicorp/cloudinit/latest/docs/data-sources/config)                                | data source |

## Inputs

| Name                                                                     | Description                                        | Type          | Default       | Required |
| ------------------------------------------------------------------------ | -------------------------------------------------- | ------------- | ------------- | :------: |
| <a name="input_bastion_users"></a> [bastion_users](#input_bastion_users) | Users and their SSH pub keys                       | `map(string)` | n/a           |   yes    |
| <a name="input_instance_type"></a> [instance_type](#input_instance_type) | The instance type to use, must be graviton (arm64) | `string`      | `"t4g.micro"` |    no    |
| <a name="input_name"></a> [name](#input_name)                            | The name of the instance to be created             | `string`      | n/a           |   yes    |
| <a name="input_subnet_id"></a> [subnet_id](#input_subnet_id)             | Which public subnet should this be in              | `string`      | n/a           |   yes    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                   | Tags to apply to resources                         | `map(string)` | n/a           |   yes    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                      | ID of the VPC                                      | `string`      | n/a           |   yes    |

## Outputs

| Name                                                                                                                 | Description                                             |
| -------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------- |
| <a name="output_bastion_private_dns"></a> [bastion_private_dns](#output_bastion_private_dns)                         | The private DNS name of the Bastion instance            |
| <a name="output_bastion_private_ip"></a> [bastion_private_ip](#output_bastion_private_ip)                            | The private IP address of the Bastion instance          |
| <a name="output_bastion_public_dns"></a> [bastion_public_dns](#output_bastion_public_dns)                            | The public DNS name of the Bastion instance             |
| <a name="output_bastion_public_ip"></a> [bastion_public_ip](#output_bastion_public_ip)                               | The public IP address of the Bastion instance           |
| <a name="output_bastion_security_group_arn"></a> [bastion_security_group_arn](#output_bastion_security_group_arn)    | The security group ARN of the Bastion instance          |
| <a name="output_bastion_security_group_id"></a> [bastion_security_group_id](#output_bastion_security_group_id)       | The security group ID of the Bastion instance           |
| <a name="output_bastion_security_group_name"></a> [bastion_security_group_name](#output_bastion_security_group_name) | The name of the security group for the Bastion instance |

<!-- END_TF_DOCS -->
