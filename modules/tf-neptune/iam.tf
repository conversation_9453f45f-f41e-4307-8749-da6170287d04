data "aws_iam_policy_document" "neptune_assume_role_policy" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["rds.amazonaws.com"]
    }
    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "neptune_role" {
  name               = "${var.cluster_identifier}-role"
  assume_role_policy = data.aws_iam_policy_document.neptune_assume_role_policy.json

  tags = var.tag_map
}

resource "aws_iam_role_policy_attachment" "this" {
  role       = aws_iam_role.neptune_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/ROSAKMSProviderPolicy"
}
