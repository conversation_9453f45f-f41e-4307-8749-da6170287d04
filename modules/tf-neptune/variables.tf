variable "allow_major_version_upgrade" {
  type        = bool
  description = "Allow major version upgrades for the Neptune cluster"
  default     = false
}

variable "apply_immediately" {
  type        = bool
  description = "Apply changes immediately"
  default     = true
}

variable "backup_retention_period" {
  type        = number
  description = "Number of days to retain backups"
  default     = 7
}

variable "cluster_identifier" {
  type        = string
  description = "Identifier for the Neptune cluster"
}

variable "deletion_protection" {
  type        = bool
  description = "Whether deletion protection is enabled"
  default     = false
}

variable "engine_version" {
  type        = string
  description = "Neptune engine version"
  default     = "*******"
}

variable "iam_database_authentication_enabled" {
  type        = bool
  description = "Enable IAM database authentication"
  default     = false
}

variable "instance_class" {
  type        = string
  description = "Instance class for Neptune cluster instances"
  default     = "db.t4g.medium"
}

variable "instance_count" {
  type        = number
  description = "Number of Neptune cluster instances"
  default     = 2
}

variable "kms_key_arn" {
  type        = string
  description = "KMS key ARN for encryption at rest"
  default     = null
}

variable "preferred_backup_window" {
  type        = string
  description = "Preferred backup window"
  default     = "03:00-04:00"
}

variable "skip_final_snapshot" {
  type        = bool
  description = "Skip final snapshot before deletion"
  default     = true
}

variable "subnet_ids" {
  type        = set(string)
  description = "Set of subnet IDs for the Neptune cluster"
}

variable "tag_map" {
  type        = map(string)
  description = "Tags to apply to resources"
}

variable "vpc_cidr_block" {
  type        = string
  description = "CIDR block of the VPC"
}

variable "vpc_id" {
  type        = string
  description = "ID of the VPC"
}

variable "additional_ingress_cidrs" {
  type        = list(string)
  description = "Additional CIDR blocks allowed to access the Neptune cluster"
  default     = []
}

variable "enable_serverless" {
  type        = bool
  description = "Enable serverless mode"
  default     = false
}

variable "min_capacity" {
  type        = number
  description = "Minimum capacity for serverless Neptune cluster"
  default     = 2.5
}

variable "max_capacity" {
  type        = number
  description = "Maximum capacity for serverless Neptune cluster"
  default     = 15
}

variable "log_retention_in_days" {
  type        = number
  description = "Retention period for CloudWatch logs"
  default     = 90
}

variable "availability_zones" {
  type        = list(string)
  description = "List of availability zones for Neptune instances"
}

variable "parameter_group_family" {
  type        = string
  description = "Specifies the family of the Neptune parameter group to use. The family determines the version compatibility for the parameter group settings, based on the Neptune engine version."
  default     = "neptune1.3"
}

variable "export_logs_to_cloudwatch" {
  type        = set(string)
  description = "What logs do you want to export to Cloudwatch. 'audit' and 'slowquery' are supported"
  default     = []

  validation {
    condition     = alltrue([for log in var.export_logs_to_cloudwatch : log == "audit" || log == "slowquery"])
    error_message = "Only 'audit' and 'slowquery' are valid values for export_logs_to_cloudwatch."
  }
}
