# tf-neptune

## Example

```terraform
module "neptune" {
  source = "./modules/neptune"

  cluster_identifier                  = "my-neptune-cluster"
  instance_class                      = "db.t4g.medium"
  instance_count                      = 2
  engine_version                      = "*******"
  iam_database_authentication_enabled = false
  preferred_backup_window             = "03:00-04:00"
  skip_final_snapshot                 = true
  deletion_protection                 = false
  parameter_group_name                = "my-neptune-parameter-group"
  export_logs_to_cloudwatch           = ["audit", "slowquery"]
  log_retention_in_days               = 90
  enable_serverless                   = false
  min_capacity                        = 2.5
  max_capacity                        = 15

  vpc_cidr_block           = module.vpc.vpc.cidr_block
  vpc_id                   = module.vpc.vpc.id
  subnet_ids               = module.vpc.subnets.private[*].id
  additional_ingress_cidrs = []

  tag_map = {
    Environment = "production"
    Project     = "neptune-db"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version |
| ------------------------------------------------------ | ------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                     | Type        |
| -------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_cloudwatch_log_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group)                        | resource    |
| [aws_iam_role.neptune_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                        | resource    |
| [aws_iam_role_policy_attachment.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)            | resource    |
| [aws_neptune_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/neptune_cluster)                                  | resource    |
| [aws_neptune_cluster_instance.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/neptune_cluster_instance)                | resource    |
| [aws_neptune_cluster_parameter_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/neptune_cluster_parameter_group)  | resource    |
| [aws_neptune_subnet_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/neptune_subnet_group)                        | resource    |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)                                    | resource    |
| [aws_security_group_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)                       | resource    |
| [aws_iam_policy_document.neptune_assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name                                                                                                                                       | Description                                                                                                                                                                        | Type           | Default           | Required |
| ------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------- | ----------------- | :------: |
| <a name="input_additional_ingress_cidrs"></a> [additional_ingress_cidrs](#input_additional_ingress_cidrs)                                  | Additional CIDR blocks allowed to access the Neptune cluster                                                                                                                       | `list(string)` | `[]`              |    no    |
| <a name="input_allow_major_version_upgrade"></a> [allow_major_version_upgrade](#input_allow_major_version_upgrade)                         | Allow major version upgrades for the Neptune cluster                                                                                                                               | `bool`         | `false`           |    no    |
| <a name="input_apply_immediately"></a> [apply_immediately](#input_apply_immediately)                                                       | Apply changes immediately                                                                                                                                                          | `bool`         | `false`           |    no    |
| <a name="input_availability_zones"></a> [availability_zones](#input_availability_zones)                                                    | List of availability zones for Neptune instances                                                                                                                                   | `list(string)` | n/a               |   yes    |
| <a name="input_backup_retention_period"></a> [backup_retention_period](#input_backup_retention_period)                                     | Number of days to retain backups                                                                                                                                                   | `number`       | `7`               |    no    |
| <a name="input_cluster_identifier"></a> [cluster_identifier](#input_cluster_identifier)                                                    | Identifier for the Neptune cluster                                                                                                                                                 | `string`       | n/a               |   yes    |
| <a name="input_deletion_protection"></a> [deletion_protection](#input_deletion_protection)                                                 | Whether deletion protection is enabled                                                                                                                                             | `bool`         | `false`           |    no    |
| <a name="input_enable_serverless"></a> [enable_serverless](#input_enable_serverless)                                                       | Enable serverless mode                                                                                                                                                             | `bool`         | `false`           |    no    |
| <a name="input_engine_version"></a> [engine_version](#input_engine_version)                                                                | Neptune engine version                                                                                                                                                             | `string`       | `"*******"`       |    no    |
| <a name="input_export_logs_to_cloudwatch"></a> [export_logs_to_cloudwatch](#input_export_logs_to_cloudwatch)                               | What logs do you want to export to Cloudwatch. 'audit' and 'slowquery' are supported                                                                                               | `set(string)`  | `[]`              |    no    |
| <a name="input_iam_database_authentication_enabled"></a> [iam_database_authentication_enabled](#input_iam_database_authentication_enabled) | Enable IAM database authentication                                                                                                                                                 | `bool`         | `false`           |    no    |
| <a name="input_instance_class"></a> [instance_class](#input_instance_class)                                                                | Instance class for Neptune cluster instances                                                                                                                                       | `string`       | `"db.t4g.medium"` |    no    |
| <a name="input_instance_count"></a> [instance_count](#input_instance_count)                                                                | Number of Neptune cluster instances                                                                                                                                                | `number`       | `2`               |    no    |
| <a name="input_kms_key_arn"></a> [kms_key_arn](#input_kms_key_arn)                                                                         | KMS key ARN for encryption at rest                                                                                                                                                 | `string`       | `null`            |    no    |
| <a name="input_log_retention_in_days"></a> [log_retention_in_days](#input_log_retention_in_days)                                           | Retention period for CloudWatch logs                                                                                                                                               | `number`       | `90`              |    no    |
| <a name="input_max_capacity"></a> [max_capacity](#input_max_capacity)                                                                      | Maximum capacity for serverless Neptune cluster                                                                                                                                    | `number`       | `15`              |    no    |
| <a name="input_min_capacity"></a> [min_capacity](#input_min_capacity)                                                                      | Minimum capacity for serverless Neptune cluster                                                                                                                                    | `number`       | `2.5`             |    no    |
| <a name="input_parameter_group_family"></a> [parameter_group_family](#input_parameter_group_family)                                        | Specifies the family of the Neptune parameter group to use. The family determines the version compatibility for the parameter group settings, based on the Neptune engine version. | `string`       | `"neptune1.3"`    |    no    |
| <a name="input_parameter_group_name"></a> [parameter_group_name](#input_parameter_group_name)                                              | Name of the Neptune parameter group                                                                                                                                                | `string`       | n/a               |   yes    |
| <a name="input_preferred_backup_window"></a> [preferred_backup_window](#input_preferred_backup_window)                                     | Preferred backup window                                                                                                                                                            | `string`       | `"03:00-04:00"`   |    no    |
| <a name="input_skip_final_snapshot"></a> [skip_final_snapshot](#input_skip_final_snapshot)                                                 | Skip final snapshot before deletion                                                                                                                                                | `bool`         | `true`            |    no    |
| <a name="input_subnet_ids"></a> [subnet_ids](#input_subnet_ids)                                                                            | Set of subnet IDs for the Neptune cluster                                                                                                                                          | `set(string)`  | n/a               |   yes    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                                                                                     | Tags to apply to resources                                                                                                                                                         | `map(string)`  | n/a               |   yes    |
| <a name="input_vpc_cidr_block"></a> [vpc_cidr_block](#input_vpc_cidr_block)                                                                | CIDR block of the VPC                                                                                                                                                              | `string`       | n/a               |   yes    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                                                        | ID of the VPC                                                                                                                                                                      | `string`       | n/a               |   yes    |

## Outputs

| Name                                                                                                                                            | Description                                               |
| ----------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------- |
| <a name="output_neptune_cluster_endpoint"></a> [neptune_cluster_endpoint](#output_neptune_cluster_endpoint)                                     | The endpoint of the Neptune cluster                       |
| <a name="output_neptune_cluster_port"></a> [neptune_cluster_port](#output_neptune_cluster_port)                                                 | The port used by the Neptune cluster                      |
| <a name="output_neptune_cluster_reader_endpoint"></a> [neptune_cluster_reader_endpoint](#output_neptune_cluster_reader_endpoint)                | The reader endpoint of the Neptune cluster                |
| <a name="output_neptune_instance_endpoints"></a> [neptune_instance_endpoints](#output_neptune_instance_endpoints)                               | List of Neptune instance endpoints                        |
| <a name="output_neptune_security_group_id"></a> [neptune_security_group_id](#output_neptune_security_group_id)                                  | The security group ID associated with the Neptune cluster |
| <a name="output_neptune_security_group_ingress_rules"></a> [neptune_security_group_ingress_rules](#output_neptune_security_group_ingress_rules) | The ingress rules for the Neptune security group          |
| <a name="output_neptune_subnet_group_name"></a> [neptune_subnet_group_name](#output_neptune_subnet_group_name)                                  | The name of the Neptune subnet group                      |

<!-- END_TF_DOCS -->
