resource "aws_security_group" "this" {
  name   = "neptune-${var.cluster_identifier}"
  vpc_id = var.vpc_id
  tags   = var.tag_map
}

resource "aws_security_group_rule" "ingress" {
  for_each = toset(concat([var.vpc_cidr_block], var.additional_ingress_cidrs))

  security_group_id = aws_security_group.this.id
  type              = "ingress"
  from_port         = 8182
  to_port           = 8182
  protocol          = "tcp"
  cidr_blocks       = [each.value]
}

resource "aws_neptune_subnet_group" "this" {
  name       = "neptune-${var.cluster_identifier}"
  subnet_ids = var.subnet_ids
  tags       = var.tag_map
}
