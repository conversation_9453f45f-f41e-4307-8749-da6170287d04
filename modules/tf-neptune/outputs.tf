output "neptune_cluster_endpoint" {
  description = "The endpoint of the Neptune cluster"
  value       = aws_neptune_cluster.this.endpoint
}

output "neptune_cluster_reader_endpoint" {
  description = "The reader endpoint of the Neptune cluster"
  value       = aws_neptune_cluster.this.reader_endpoint
}

output "neptune_cluster_port" {
  description = "The port used by the Neptune cluster"
  value       = 8182
}

output "neptune_instance_endpoints" {
  description = "List of Neptune instance endpoints"
  value       = [for instance in aws_neptune_cluster_instance.this : instance.endpoint]
}

output "neptune_security_group_id" {
  description = "The security group ID associated with the Neptune cluster"
  value       = aws_security_group.this.id
}

output "neptune_security_group_ingress_rules" {
  description = "The ingress rules for the Neptune security group"
  value       = aws_security_group_rule.ingress
}

output "neptune_subnet_group_name" {
  description = "The name of the Neptune subnet group"
  value       = aws_neptune_subnet_group.this.name
}
