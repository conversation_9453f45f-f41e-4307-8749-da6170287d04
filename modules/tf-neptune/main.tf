resource "aws_neptune_cluster" "this" {
  allow_major_version_upgrade          = var.allow_major_version_upgrade
  apply_immediately                    = var.apply_immediately
  backup_retention_period              = var.backup_retention_period
  cluster_identifier                   = var.cluster_identifier
  deletion_protection                  = var.deletion_protection
  enable_cloudwatch_logs_exports       = length(var.export_logs_to_cloudwatch) > 0 ? var.export_logs_to_cloudwatch : null
  engine                               = "neptune"
  engine_version                       = var.engine_version
  iam_database_authentication_enabled  = var.iam_database_authentication_enabled
  iam_roles                            = [aws_iam_role.neptune_role.arn]
  kms_key_arn                          = try(var.kms_key_arn, null)
  neptune_cluster_parameter_group_name = aws_neptune_cluster_parameter_group.this.name
  neptune_subnet_group_name            = aws_neptune_subnet_group.this.name
  preferred_backup_window              = var.preferred_backup_window
  skip_final_snapshot                  = var.skip_final_snapshot
  storage_encrypted                    = true
  vpc_security_group_ids               = [aws_security_group.this.id]

  dynamic "serverless_v2_scaling_configuration" {
    for_each = var.enable_serverless ? [1] : []
    content {
      min_capacity = var.min_capacity
      max_capacity = var.max_capacity
    }
  }

  tags = merge(var.tag_map, { Cluster = var.cluster_identifier })
}

resource "aws_neptune_cluster_instance" "this" {
  count = var.instance_count

  cluster_identifier = aws_neptune_cluster.this.cluster_identifier
  identifier         = "${var.cluster_identifier}-${count.index}"
  instance_class     = var.instance_class
  apply_immediately  = var.apply_immediately
  availability_zone  = element(var.availability_zones, count.index)
  tags               = merge(var.tag_map, { Instance = "${var.cluster_identifier}-${count.index}" })
}

resource "aws_neptune_cluster_parameter_group" "this" {
  name   = "${var.cluster_identifier}-parameter-group"
  family = var.parameter_group_family
  tags   = var.tag_map

  parameter {
    name  = "neptune_enable_audit_log"
    value = "1"
  }
}

resource "aws_cloudwatch_log_group" "this" {
  for_each = toset(
    [
      "/aws/neptune/${var.cluster_identifier}/audit",
      "/aws/neptune/${var.cluster_identifier}/slowquery"
    ]
  )

  name              = each.value
  retention_in_days = var.log_retention_in_days
  tags              = var.tag_map
}

