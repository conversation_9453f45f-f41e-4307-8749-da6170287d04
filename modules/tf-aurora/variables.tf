variable "additional_ingress_cidrs" {
  type        = list(string)
  description = "Additional CIDR ranges that should have access to this resource"
  default     = []
}

variable "cluster_identifier" {
  type        = string
  description = "Cluster identifier"
  default     = "app-db"
}

variable "deletion_protection" {
  description = "Enable deletion protection for the RDS cluster"
  type        = bool
  default     = true
}

variable "instance_class" {
  type        = string
  description = "Which instance type to use for nodes in the cluster"
}

variable "instance_count" {
  type        = number
  description = "The number of RDS cluster instances to create"
  default     = 2

  validation {
    condition     = var.instance_count >= 2
    error_message = "You must specify at least two instances."
  }
}

variable "master_username" {
  description = "The master username for the database"
  type        = string
  default     = "aurora"
}

variable "monitoring_interval" {
  description = "Interval in seconds between points when Enhanced Monitoring metrics are collected. Specify 0 to disable. Valid values are 0, 1, 5, 10, 15, 30, 60"
  type        = number
  default     = 0

  validation {
    condition     = contains([1, 5, 10, 15, 30, 60], var.monitoring_interval)
    error_message = "monitoring_interval must be one of the following values: 1, 5, 10, 15, 30, 60 (in seconds)"
  }
}

variable "skip_final_snapshot" {
  description = "Determines whether a final DB snapshot is created before the DB cluster is deleted"
  type        = bool
  default     = false
}

variable "subnet_ids" {
  type        = set(string)
  description = "Which subnets to deploy into"
}

variable "tag_map" {
  type        = map(string)
  description = "A map of tags to assign to resources"
  default = {
    Name           = "Foo"
    BillingProject = "FooBarBazCo"
  }
}

variable "vpc_cidr_block" {
  type        = string
  description = "The VPC cidr this is being installed into"
}

variable "vpc_id" {
  type        = string
  description = "The VPC id"
}

variable "engine_version" {
  description = "The database engine version for Aurora Postgres"
  type        = string
}
