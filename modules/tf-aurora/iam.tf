data "aws_iam_policy_document" "monitoring_assume" {
  statement {
    sid     = "AssumePolicy"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["monitoring.rds.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "monitoring" {
  name               = "RDSMonitoring-${var.cluster_identifier}"
  assume_role_policy = data.aws_iam_policy_document.monitoring_assume.json
  tags               = var.tag_map
}

resource "aws_iam_role_policy_attachment" "monitoring" {
  role       = aws_iam_role.monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

