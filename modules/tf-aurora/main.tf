data "aws_caller_identity" "current" {}

resource "random_uuid" "final_snapshot" {}

resource "aws_rds_cluster" "this" {
  backup_retention_period             = 7
  cluster_identifier                  = var.cluster_identifier
  database_name                       = "aurora"
  db_subnet_group_name                = aws_db_subnet_group.this.name
  vpc_security_group_ids              = [aws_security_group.this.id]
  deletion_protection                 = var.deletion_protection
  enabled_cloudwatch_logs_exports     = ["postgresql"]
  engine                              = "aurora-postgresql"
  engine_version                      = var.engine_version
  final_snapshot_identifier           = "${var.cluster_identifier}-final-snapshot-${random_uuid.final_snapshot.result}"
  iam_database_authentication_enabled = true
  manage_master_user_password         = true
  master_username                     = var.master_username
  port                                = 5432
  preferred_maintenance_window        = "sat:01:00-sat:04:00"
  skip_final_snapshot                 = var.skip_final_snapshot
  storage_encrypted                   = true
  tags                                = var.tag_map
}

resource "aws_rds_cluster_instance" "this" {
  count = var.instance_count

  ca_cert_identifier                    = "rds-ca-rsa2048-g1"
  cluster_identifier                    = aws_rds_cluster.this.id
  identifier                            = "${var.cluster_identifier}-${count.index + 1}"
  instance_class                        = var.instance_class
  engine                                = aws_rds_cluster.this.engine
  engine_version                        = aws_rds_cluster.this.engine_version
  monitoring_interval                   = var.monitoring_interval
  monitoring_role_arn                   = aws_iam_role.monitoring.arn
  performance_insights_enabled          = true
  performance_insights_retention_period = 731 # 2 years
  preferred_maintenance_window          = "sun:01:00-sun:04:00"
  tags                                  = merge(var.tag_map, { Name = "${var.cluster_identifier}-${count.index + 1}" })
}

resource "aws_secretsmanager_secret_rotation" "this" {
  secret_id = aws_rds_cluster.this.master_user_secret[0].secret_arn

  rotation_rules {
    automatically_after_days = 30
  }
}
