locals {
  port = 5432
}

resource "aws_db_subnet_group" "this" {
  name       = var.cluster_identifier
  subnet_ids = var.subnet_ids
  tags       = merge(var.tag_map, { Name = "${var.cluster_identifier} Subnet Group" })
}

resource "aws_security_group" "this" {
  name        = var.cluster_identifier
  description = "${var.cluster_identifier} Aurora cluster ecurity roup"
  vpc_id      = var.vpc_id
  tags        = var.tag_map
}

resource "aws_security_group_rule" "ingress" {
  for_each = toset(concat([var.vpc_cidr_block], var.additional_ingress_cidrs))

  security_group_id = aws_security_group.this.id
  type              = "ingress"
  from_port         = local.port
  to_port           = local.port
  protocol          = "tcp"
  cidr_blocks       = [each.value]
}
