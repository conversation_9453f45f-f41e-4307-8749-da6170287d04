# tf-aurora

## Example

```terraform
module "aurora" {
  source = "./modules/aurora"

  cluster_identifier = "pg-app-cluster"
  instance_class     = "db.t4g.medium"
  instance_count     = 2
  master_username    = "pgsuperuser"
  engine_version     = "16.5"

  vpc_cidr_block = module.vpc.vpc.cidr_block
  vpc_id         = module.vpc.vpc.id
  subnet_ids     = module.vpc.subnets.private[*].id
  additional_ingress_cidrs = [
    "192.168.1.0/24",
    "10.100.0.0/16"
  ]

  tag_map = {
    Name           = "WebApps"
    BillingProject = "MyProject"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                            | Version  |
| --------------------------------------------------------------- | -------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws)          | >= 5.0   |
| <a name="requirement_random"></a> [random](#requirement_random) | >= 3.6.0 |

## Modules

No modules.

## Resources

| Name                                                                                                                                                  | Type        |
| ----------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_db_subnet_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_subnet_group)                               | resource    |
| [aws_iam_role.monitoring](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                       | resource    |
| [aws_iam_role_policy_attachment.monitoring](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)   | resource    |
| [aws_rds_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster)                                       | resource    |
| [aws_rds_cluster_instance.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster_instance)                     | resource    |
| [aws_secretsmanager_secret_rotation.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret_rotation) | resource    |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)                                 | resource    |
| [aws_security_group_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)                    | resource    |
| [random_uuid.final_snapshot](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/uuid)                                     | resource    |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity)                         | data source |
| [aws_iam_policy_document.monitoring_assume](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)       | data source |

## Inputs

| Name                                                                                                      | Description                                                                                                                                       | Type           | Default                                                                     | Required |
| --------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- | -------------- | --------------------------------------------------------------------------- | :------: |
| <a name="input_additional_ingress_cidrs"></a> [additional_ingress_cidrs](#input_additional_ingress_cidrs) | Additional CIDR ranges that should have access to this resource                                                                                   | `list(string)` | `[]`                                                                        |    no    |
| <a name="input_cluster_identifier"></a> [cluster_identifier](#input_cluster_identifier)                   | Cluster identifier                                                                                                                                | `string`       | `"app-db"`                                                                  |    no    |
| <a name="input_deletion_protection"></a> [deletion_protection](#input_deletion_protection)                | Enable deletion protection for the RDS cluster                                                                                                    | `bool`         | `true`                                                                      |    no    |
| <a name="input_engine_version"></a> [engine_version](#input_engine_version)                               | The database engine version for Aurora Postgres                                                                                                   | `string`       | n/a                                                                         |   yes    |
| <a name="input_instance_class"></a> [instance_class](#input_instance_class)                               | Which instance type to use for nodes in the cluster                                                                                               | `string`       | n/a                                                                         |   yes    |
| <a name="input_instance_count"></a> [instance_count](#input_instance_count)                               | The number of RDS cluster instances to create                                                                                                     | `number`       | `2`                                                                         |    no    |
| <a name="input_master_username"></a> [master_username](#input_master_username)                            | The master username for the database                                                                                                              | `string`       | `"aurora"`                                                                  |    no    |
| <a name="input_monitoring_interval"></a> [monitoring_interval](#input_monitoring_interval)                | Interval in seconds between points when Enhanced Monitoring metrics are collected. Specify 0 to disable. Valid values are 0, 1, 5, 10, 15, 30, 60 | `number`       | `0`                                                                         |    no    |
| <a name="input_skip_final_snapshot"></a> [skip_final_snapshot](#input_skip_final_snapshot)                | Determines whether a final DB snapshot is created before the DB cluster is deleted                                                                | `bool`         | `false`                                                                     |    no    |
| <a name="input_subnet_ids"></a> [subnet_ids](#input_subnet_ids)                                           | Which subnets to deploy into                                                                                                                      | `set(string)`  | n/a                                                                         |   yes    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                                                    | A map of tags to assign to resources                                                                                                              | `map(string)`  | <pre>{<br/> "BillingProject": "FooBarBazCo",<br/> "Name": "Foo"<br/>}</pre> |    no    |
| <a name="input_vpc_cidr_block"></a> [vpc_cidr_block](#input_vpc_cidr_block)                               | The VPC cidr this is being installed into                                                                                                         | `string`       | n/a                                                                         |   yes    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                       | The VPC id                                                                                                                                        | `string`       | n/a                                                                         |   yes    |

## Outputs

| Name                                                                                                     | Description                                                                            |
| -------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------- |
| <a name="output_cluster_arn"></a> [cluster_arn](#output_cluster_arn)                                     | Cluster ARN                                                                            |
| <a name="output_cluster_endpoint"></a> [cluster_endpoint](#output_cluster_endpoint)                      | DNS address of the RDS instance                                                        |
| <a name="output_cluster_port"></a> [cluster_port](#output_cluster_port)                                  | Cluster Port                                                                           |
| <a name="output_cluster_reader_endpoint"></a> [cluster_reader_endpoint](#output_cluster_reader_endpoint) | Read-only endpoint for the Aurora cluster, automatically load-balanced across replicas |
| <a name="output_master_secret_arn"></a> [master_secret_arn](#output_master_secret_arn)                   | The ARN of the secrets manager secret RDS creates for the master user                  |

<!-- END_TF_DOCS -->
