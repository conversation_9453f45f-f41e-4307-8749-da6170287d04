output "cluster_arn" {
  description = "Cluster ARN"
  value       = aws_rds_cluster.this.arn
}

output "cluster_endpoint" {
  description = "DNS address of the RDS instance"
  value       = aws_rds_cluster.this.endpoint
}

output "cluster_port" {
  description = "Cluster Port"
  value       = aws_rds_cluster.this.port
}

output "cluster_reader_endpoint" {
  description = "Read-only endpoint for the Aurora cluster, automatically load-balanced across replicas"
  value       = aws_rds_cluster.this.reader_endpoint
}

output "master_secret_arn" {
  description = "The ARN of the secrets manager secret RDS creates for the master user"
  value       = aws_rds_cluster.this.master_user_secret[0].secret_arn
}
