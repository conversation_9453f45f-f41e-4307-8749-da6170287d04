variable "engine_version" {
  type        = string
  description = "What version of Valkey"
  default     = "7.2"
}

variable "shard_count" {
  type        = number
  description = "How many shards in the cluster"
  default     = 1
}

variable "additional_ingress_cidrs" {
  type        = list(string)
  description = "List of additional CIDR ranges allowed to access the OpenSearch domain"
  default     = []
}

variable "vpc_cidr_block" {
  type        = string
  description = "CIDR block of the VPC where the Elasticache cluster will be deployed"
}

variable "name" {
  type        = string
  description = "The name of the Elasticache replication group"
}

variable "instance_type" {
  type        = string
  description = "The instance type for the Elasticache nodes (e.g., cache.t4g.small)."
  default     = "db.r7g.large"
}

variable "subnet_ids" {
  type        = set(string)
  description = "List of VPC Subnet IDs for the cache subnet group"
}

variable "tag_map" {
  type        = map(string)
  description = "Map of tags to apply to the created resources"
  default     = {}
}

variable "snapshot_retention_limit" {
  description = "The number of days for which MemoryDB retains automatic snapshots before deleting them. When set to `0`, automatic backups are disabled"
  type        = number
  default     = 7
}

variable "vpc_id" {
  type        = string
  description = "ID of the VPC where the Elasticache cluster will be deployed"
}
