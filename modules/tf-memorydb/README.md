# tf-memorydb

## Example

```terraform
module "memdb" {
  source = "./modules/tf-memorydb"

  name           = "mm-cache"
  instance_type  = "db.t4g.small"
  vpc_id         = module.vpc.vpc.id
  vpc_cidr_block = module.vpc.vpc.cidr_block
  subnet_ids     = module.vpc.subnets.private[*].id
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version   |
| ------------------------------------------------------ | --------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.78.0 |

## Modules

No modules.

## Resources

| Name                                                                                                                                | Type     |
| ----------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [aws_memorydb_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/memorydb_cluster)           | resource |
| [aws_memorydb_subnet_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/memorydb_subnet_group) | resource |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)               | resource |
| [aws_security_group_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)  | resource |

## Inputs

| Name                                                                                                      | Description                                                                                                                             | Type           | Default          | Required |
| --------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- | -------------- | ---------------- | :------: |
| <a name="input_additional_ingress_cidrs"></a> [additional_ingress_cidrs](#input_additional_ingress_cidrs) | List of additional CIDR ranges allowed to access the OpenSearch domain                                                                  | `list(string)` | `[]`             |    no    |
| <a name="input_engine_version"></a> [engine_version](#input_engine_version)                               | What version of Valkey                                                                                                                  | `string`       | `"7.2"`          |    no    |
| <a name="input_instance_type"></a> [instance_type](#input_instance_type)                                  | The instance type for the Elasticache nodes (e.g., cache.t4g.small).                                                                    | `string`       | `"db.r7g.large"` |    no    |
| <a name="input_name"></a> [name](#input_name)                                                             | The name of the Elasticache replication group                                                                                           | `string`       | n/a              |   yes    |
| <a name="input_shard_count"></a> [shard_count](#input_shard_count)                                        | How many shards in the cluster                                                                                                          | `number`       | `1`              |    no    |
| <a name="input_snapshot_retention_limit"></a> [snapshot_retention_limit](#input_snapshot_retention_limit) | The number of days for which MemoryDB retains automatic snapshots before deleting them. When set to `0`, automatic backups are disabled | `number`       | `7`              |    no    |
| <a name="input_subnet_ids"></a> [subnet_ids](#input_subnet_ids)                                           | List of VPC Subnet IDs for the cache subnet group                                                                                       | `set(string)`  | n/a              |   yes    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                                                    | Map of tags to apply to the created resources                                                                                           | `map(string)`  | `{}`             |    no    |
| <a name="input_vpc_cidr_block"></a> [vpc_cidr_block](#input_vpc_cidr_block)                               | CIDR block of the VPC where the Elasticache cluster will be deployed                                                                    | `string`       | n/a              |   yes    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                       | ID of the VPC where the Elasticache cluster will be deployed                                                                            | `string`       | n/a              |   yes    |

## Outputs

No outputs.

<!-- END_TF_DOCS -->
