resource "aws_memorydb_cluster" "this" {
  acl_name                 = "open-access"
  name                     = var.name
  node_type                = var.instance_type
  engine                   = "valkey"
  engine_version           = var.engine_version
  num_shards               = var.shard_count
  security_group_ids       = [aws_security_group.this.id]
  snapshot_retention_limit = var.snapshot_retention_limit
  subnet_group_name        = aws_memorydb_subnet_group.this.id
}

resource "aws_memorydb_subnet_group" "this" {
  name       = var.name
  subnet_ids = var.subnet_ids
  tags       = merge(var.tag_map, { Name = "Valkey ${var.name}" })
}

resource "aws_security_group" "this" {
  name   = "memorydb-valkey-${var.name}"
  vpc_id = var.vpc_id
  tags   = var.tag_map
}

resource "aws_security_group_rule" "ingress" {
  for_each = toset(concat([var.vpc_cidr_block], var.additional_ingress_cidrs))

  security_group_id = aws_security_group.this.id
  type              = "ingress"
  from_port         = 6379
  to_port           = 6379
  protocol          = "tcp"
  cidr_blocks       = [each.value]
}

