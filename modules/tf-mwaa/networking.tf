resource "aws_security_group" "this" {
  name        = "mwaa-${var.name}-"
  description = "Security group for MWAA environment ${var.name}"
  vpc_id      = var.vpc_id

  tags = var.tag_map
}

resource "aws_security_group_rule" "mwaa_inbound" {
  type                     = "ingress"
  from_port                = 0
  to_port                  = 0
  protocol                 = "all"
  source_security_group_id = aws_security_group.this.id
  security_group_id        = aws_security_group.this.id
  description              = "Amazon MWAA inbound access"
}

resource "aws_security_group_rule" "ui_ingress" {
  for_each = toset(concat([var.vpc_cidr_block], var.additional_ingress_cidrs))

  security_group_id = aws_security_group.this.id
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = [each.value]
  description       = "Access to Airflow UI"
}

resource "aws_security_group_rule" "mwaa_outbound" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "all"
  security_group_id = aws_security_group.this.id
  cidr_blocks       = ["0.0.0.0/0"]
  description       = "Amazon MWAA outbound access"
}
