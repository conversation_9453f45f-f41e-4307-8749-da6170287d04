locals {
  default_airflow_configuration_options = {
    "logging.logging_level" = "INFO"
  }
  airflow_configuration_options = merge(local.default_airflow_configuration_options, var.airflow_configuration_options)
}

resource "aws_mwaa_environment" "this" {
  name        = var.name
  min_workers = var.min_workers
  max_workers = var.max_workers

  airflow_configuration_options    = local.airflow_configuration_options
  airflow_version                  = var.airflow_version
  dag_s3_path                      = var.dag_s3_path
  environment_class                = var.environment_class
  execution_role_arn               = aws_iam_role.this.arn
  plugins_s3_object_version        = var.plugins_s3_object_version
  plugins_s3_path                  = var.plugins_s3_path
  requirements_s3_object_version   = var.requirements_s3_object_version
  requirements_s3_path             = var.requirements_s3_path
  schedulers                       = var.schedulers
  source_bucket_arn                = aws_s3_bucket.this.arn
  startup_script_s3_object_version = var.startup_script_s3_object_version
  startup_script_s3_path           = var.startup_script_s3_path
  webserver_access_mode            = "PUBLIC_ONLY"
  weekly_maintenance_window_start  = "SAT:01:00"
  tags                             = var.tag_map

  network_configuration {
    security_group_ids = concat([aws_security_group.this.id], var.additional_security_group_ids)
    subnet_ids         = var.subnet_ids
  }

  logging_configuration {
    dag_processing_logs {
      enabled   = true
      log_level = var.dag_processing_log_level
    }

    scheduler_logs {
      enabled   = true
      log_level = var.scheduler_log_level
    }

    task_logs {
      enabled   = true
      log_level = var.task_log_level
    }

    webserver_logs {
      enabled   = true
      log_level = var.webserver_log_level
    }

    worker_logs {
      enabled   = true
      log_level = var.worker_log_level
    }
  }

  lifecycle {
    ignore_changes = [
      plugins_s3_object_version,
      requirements_s3_object_version,
      startup_script_s3_object_version
    ]
  }
}
