variable "additional_ingress_cidrs" {
  type        = list(string)
  description = "List of additional CIDR ranges allowed to access Airflow"
  default     = []
}

variable "additional_principal_arns" {
  description = "List of additional AWS principal ARNs"
  type        = list(string)
  default     = []
}

variable "additional_security_group_ids" {
  description = "Security group IDs of existing security groups that should be associated with the MWAA environment"
  type        = list(string)
  default     = []
}

variable "airflow_configuration_options" {
  description = "The airflow_configuration_options parameter specifies airflow override options"
  type        = any
  default     = null
}

variable "airflow_version" {
  description = "Airflow version of your environment, the default is to use latest version that MWAA supports"
  type        = string
  default     = null
}

variable "dag_processing_log_level" {
  description = "Log level for DAG processing logs"
  type        = string
  default     = "DEBUG"
  validation {
    condition     = contains(["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"], var.dag_processing_log_level)
    error_message = "The log level for dag_processing_log_level must be one of CRITICAL, ERROR, WARNING, INFO, DEBUG."
  }
}

variable "dag_s3_path" {
  description = "The relative path to the DAG folder in the bucket"
  type        = string
  default     = "dags/"
}

variable "environment_class" {
  description = "Environment class for the cluster"
  type        = string
  default     = "mw1.small"

  validation {
    condition     = contains(["mw1.micro", "mw1.small", "mw1.medium", "mw1.large", "mw1.xlarge", "mw1.2xlarge"], var.environment_class)
    error_message = "Bad input, review AWS docs."
  }
}

variable "execution_role_arn" {
  description = "The IAM role ARN for the environment"
  type        = string
  default     = null
}

variable "iam_role_additional_policies" {
  description = "Additional policies to be added to the IAM role that's created"
  type        = map(string)
  default     = {}
}

variable "max_workers" {
  type        = number
  description = "The maximum number of workers that can be automatically scaled up"
  default     = 10

  validation {
    condition     = var.max_workers > 0 && var.max_workers < 26
    error_message = "Value needs to be between 1 and 25."
  }
}

variable "min_workers" {
  type        = number
  description = "The minimum number of workers that you want to run in your environment"
  default     = 1
}

variable "name" {
  type        = string
  description = "The name of the instance you're deploying"
}

variable "plugins_s3_object_version" {
  description = "The plugins.zip file version you want to use"
  type        = string
  default     = null
}

variable "plugins_s3_path" {
  description = "The relative path to the plugins.zip file in the bucket. If a relative path is provided in the request, then plugins_s3_object_version is required"
  type        = string
  default     = null
}

variable "requirements_s3_object_version" {
  description = "The requirements.txt file version you want to use"
  type        = string
  default     = null
}

variable "requirements_s3_path" {
  description = "The relative path to the requirements.txt file on your Amazon S3 storage bucket. If a relative path is provided in the request, then requirements_s3_object_version is required"
  type        = string
  default     = null
}

variable "schedulers" {
  description = "The number of schedulers that you want to run in your environment. v2.0.2 and above accepts 2 - 5, default 2. v1.10.12 accepts 1"
  type        = string
  default     = null
}

variable "scheduler_log_level" {
  description = "Log level for scheduler logs"
  type        = string
  default     = "INFO"
  validation {
    condition     = contains(["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"], var.scheduler_log_level)
    error_message = "The log level for scheduler_log_level must be one of CRITICAL, ERROR, WARNING, INFO, DEBUG."
  }
}

variable "source_bucket_arn" {
  description = "The ARN of the S3 bucket where the data is stored"
  type        = string
  default     = null
}

variable "startup_script_s3_object_version" {
  description = "The version of the startup shell script you want to use. You must specify the version ID that Amazon S3 assigns to the file every time you update the script"
  type        = string
  default     = null
}

variable "startup_script_s3_path" {
  description = "The relative path to the startup script hosted in the bucket. The script runs as the environment starts before starting the Apache Airflow process. Use this script to install dependencies, modify configuration options, and set environment variables"
  type        = string
  default     = null
}

variable "subnet_ids" {
  type        = set(string)
  description = "List of VPC Subnet IDs for the cache subnet group"
}

variable "tag_map" {
  type        = map(string)
  description = "Map of tags to apply to the created resources"
  default     = {}
}

variable "task_log_level" {
  description = "Log level for task logs"
  type        = string
  default     = "WARNING"
  validation {
    condition     = contains(["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"], var.task_log_level)
    error_message = "The log level for task_log_level must be one of CRITICAL, ERROR, WARNING, INFO, DEBUG."
  }
}

variable "vpc_cidr_block" {
  type        = string
  description = "CIDR block of the VPC where the Elasticache cluster will be deployed"
}

variable "vpc_id" {
  type        = string
  description = "ID of the VPC to install into"
}

variable "webserver_access_mode" {
  description = "Access mode for the Airflow web server"
  type        = string
  default     = "PRIVATE_ONLY"
}

variable "webserver_log_level" {
  description = "Log level for webserver logs"
  type        = string
  default     = "ERROR"
  validation {
    condition     = contains(["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"], var.webserver_log_level)
    error_message = "The log level for webserve_log_level must be one of CRITICAL, ERROR, WARNING, INFO, DEBUG."
  }
}

variable "worker_log_level" {
  description = "Log level for worker logs"
  type        = string
  default     = "CRITICAL"
  validation {
    condition     = contains(["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"], var.worker_log_level)
    error_message = "The log level for worker_log_level must be one of CRITICAL, ERROR, WARNING, INFO, DEBUG."
  }
}
