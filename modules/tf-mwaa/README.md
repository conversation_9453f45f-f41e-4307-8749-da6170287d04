# tf-mwaa

Terraform module to deploy `AWS Managed Workflows for Apache Airflow (MWAA)`, it's managed Airflow.

## Example

```terraform
module "mwaa" {
  source = "./modules/mwaa"

  name              = "foobarbaz"
  airflow_version   = "2.10.1"
  environment_class = "mw1.medium"

  vpc_id             = module.vpc.vpc.id
  vpc_cidr_block     = module.vpc.vpc.cidr_block
  subnet_ids         = module.vpc.subnets.private[*].id

  min_workers = 1
  max_workers = 20

  iam_role_additional_policies = {
    "some-managed-policy" = "<ENTER_POLICY_ARN1>"
  }

  dag_processing_log_level = "WARNING"
  scheduler_log_level      = "WARNING"
  task_log_level           = "WARNING"
  webserver_log_level      = "ERROR"
  worker_log_level         = "ERROR"

  airflow_configuration_options = {
    "webserver.dag_default_view" = "tree"
    "webserver.dag_orientation"  = "TB"
    "logging.logging_level"      = "INFO"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version |
| ------------------------------------------------------ | ------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                                  | Type        |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_iam_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                                                             | resource    |
| [aws_iam_role_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy)                                                               | resource    |
| [aws_iam_role_policy_attachment.additional](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)                                   | resource    |
| [aws_mwaa_environment.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/mwaa_environment)                                                             | resource    |
| [aws_s3_bucket.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket)                                                                           | resource    |
| [aws_s3_bucket_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy)                                                             | resource    |
| [aws_s3_bucket_public_access_block.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block)                                   | resource    |
| [aws_s3_bucket_server_side_encryption_configuration.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource    |
| [aws_s3_bucket_versioning.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_versioning)                                                     | resource    |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)                                                                 | resource    |
| [aws_security_group_rule.mwaa_inbound](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)                                               | resource    |
| [aws_security_group_rule.mwaa_outbound](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)                                              | resource    |
| [aws_security_group_rule.ui_ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)                                                 | resource    |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity)                                                         | data source |
| [aws_iam_policy_document.assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                      | data source |
| [aws_iam_policy_document.bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                                  | data source |
| [aws_iam_policy_document.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                                    | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region)                                                                           | data source |

## Inputs

| Name                                                                                                                              | Description                                                                                                                                                                                                                                              | Type           | Default          | Required |
| --------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------- | ---------------- | :------: |
| <a name="input_additional_ingress_cidrs"></a> [additional_ingress_cidrs](#input_additional_ingress_cidrs)                         | List of additional CIDR ranges allowed to access Airflow                                                                                                                                                                                                 | `list(string)` | `[]`             |    no    |
| <a name="input_additional_principal_arns"></a> [additional_principal_arns](#input_additional_principal_arns)                      | List of additional AWS principal ARNs                                                                                                                                                                                                                    | `list(string)` | `[]`             |    no    |
| <a name="input_additional_security_group_ids"></a> [additional_security_group_ids](#input_additional_security_group_ids)          | Security group IDs of existing security groups that should be associated with the MWAA environment                                                                                                                                                       | `list(string)` | `[]`             |    no    |
| <a name="input_airflow_configuration_options"></a> [airflow_configuration_options](#input_airflow_configuration_options)          | The airflow_configuration_options parameter specifies airflow override options                                                                                                                                                                           | `any`          | `null`           |    no    |
| <a name="input_airflow_version"></a> [airflow_version](#input_airflow_version)                                                    | Airflow version of your environment, the default is to use latest version that MWAA supports                                                                                                                                                             | `string`       | `null`           |    no    |
| <a name="input_dag_processing_log_level"></a> [dag_processing_log_level](#input_dag_processing_log_level)                         | Log level for DAG processing logs                                                                                                                                                                                                                        | `string`       | `"DEBUG"`        |    no    |
| <a name="input_dag_s3_path"></a> [dag_s3_path](#input_dag_s3_path)                                                                | The relative path to the DAG folder in the bucket                                                                                                                                                                                                        | `string`       | `"dags/"`        |    no    |
| <a name="input_environment_class"></a> [environment_class](#input_environment_class)                                              | Environment class for the cluster                                                                                                                                                                                                                        | `string`       | `"mw1.small"`    |    no    |
| <a name="input_execution_role_arn"></a> [execution_role_arn](#input_execution_role_arn)                                           | The IAM role ARN for the environment                                                                                                                                                                                                                     | `string`       | `null`           |    no    |
| <a name="input_iam_role_additional_policies"></a> [iam_role_additional_policies](#input_iam_role_additional_policies)             | Additional policies to be added to the IAM role that's created                                                                                                                                                                                           | `map(string)`  | `{}`             |    no    |
| <a name="input_max_workers"></a> [max_workers](#input_max_workers)                                                                | The maximum number of workers that can be automatically scaled up                                                                                                                                                                                        | `number`       | `10`             |    no    |
| <a name="input_min_workers"></a> [min_workers](#input_min_workers)                                                                | The minimum number of workers that you want to run in your environment                                                                                                                                                                                   | `number`       | `1`              |    no    |
| <a name="input_name"></a> [name](#input_name)                                                                                     | The name of the instance you're deploying                                                                                                                                                                                                                | `string`       | n/a              |   yes    |
| <a name="input_plugins_s3_object_version"></a> [plugins_s3_object_version](#input_plugins_s3_object_version)                      | The plugins.zip file version you want to use                                                                                                                                                                                                             | `string`       | `null`           |    no    |
| <a name="input_plugins_s3_path"></a> [plugins_s3_path](#input_plugins_s3_path)                                                    | The relative path to the plugins.zip file in the bucket. If a relative path is provided in the request, then plugins_s3_object_version is required                                                                                                       | `string`       | `null`           |    no    |
| <a name="input_requirements_s3_object_version"></a> [requirements_s3_object_version](#input_requirements_s3_object_version)       | The requirements.txt file version you want to use                                                                                                                                                                                                        | `string`       | `null`           |    no    |
| <a name="input_requirements_s3_path"></a> [requirements_s3_path](#input_requirements_s3_path)                                     | The relative path to the requirements.txt file on your Amazon S3 storage bucket. If a relative path is provided in the request, then requirements_s3_object_version is required                                                                          | `string`       | `null`           |    no    |
| <a name="input_scheduler_log_level"></a> [scheduler_log_level](#input_scheduler_log_level)                                        | Log level for scheduler logs                                                                                                                                                                                                                             | `string`       | `"INFO"`         |    no    |
| <a name="input_schedulers"></a> [schedulers](#input_schedulers)                                                                   | The number of schedulers that you want to run in your environment. v2.0.2 and above accepts 2 - 5, default 2. v1.10.12 accepts 1                                                                                                                         | `string`       | `null`           |    no    |
| <a name="input_source_bucket_arn"></a> [source_bucket_arn](#input_source_bucket_arn)                                              | The ARN of the S3 bucket where the data is stored                                                                                                                                                                                                        | `string`       | `null`           |    no    |
| <a name="input_startup_script_s3_object_version"></a> [startup_script_s3_object_version](#input_startup_script_s3_object_version) | The version of the startup shell script you want to use. You must specify the version ID that Amazon S3 assigns to the file every time you update the script                                                                                             | `string`       | `null`           |    no    |
| <a name="input_startup_script_s3_path"></a> [startup_script_s3_path](#input_startup_script_s3_path)                               | The relative path to the startup script hosted in the bucket. The script runs as the environment starts before starting the Apache Airflow process. Use this script to install dependencies, modify configuration options, and set environment variables | `string`       | `null`           |    no    |
| <a name="input_subnet_ids"></a> [subnet_ids](#input_subnet_ids)                                                                   | List of VPC Subnet IDs for the cache subnet group                                                                                                                                                                                                        | `set(string)`  | n/a              |   yes    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                                                                            | Map of tags to apply to the created resources                                                                                                                                                                                                            | `map(string)`  | `{}`             |    no    |
| <a name="input_task_log_level"></a> [task_log_level](#input_task_log_level)                                                       | Log level for task logs                                                                                                                                                                                                                                  | `string`       | `"WARNING"`      |    no    |
| <a name="input_vpc_cidr_block"></a> [vpc_cidr_block](#input_vpc_cidr_block)                                                       | CIDR block of the VPC where the Elasticache cluster will be deployed                                                                                                                                                                                     | `string`       | n/a              |   yes    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                                               | ID of the VPC to install into                                                                                                                                                                                                                            | `string`       | n/a              |   yes    |
| <a name="input_webserver_access_mode"></a> [webserver_access_mode](#input_webserver_access_mode)                                  | Access mode for the Airflow web server                                                                                                                                                                                                                   | `string`       | `"PRIVATE_ONLY"` |    no    |
| <a name="input_webserver_log_level"></a> [webserver_log_level](#input_webserver_log_level)                                        | Log level for webserver logs                                                                                                                                                                                                                             | `string`       | `"ERROR"`        |    no    |
| <a name="input_worker_log_level"></a> [worker_log_level](#input_worker_log_level)                                                 | Log level for worker logs                                                                                                                                                                                                                                | `string`       | `"CRITICAL"`     |    no    |

## Outputs

| Name                                                                                                  | Description                               |
| ----------------------------------------------------------------------------------------------------- | ----------------------------------------- |
| <a name="output_mwaa_security_group_id"></a> [mwaa_security_group_id](#output_mwaa_security_group_id) | Security group id of the MWAA Environment |
| <a name="output_mwaa_webserver_url"></a> [mwaa_webserver_url](#output_mwaa_webserver_url)             | The webserver URL of the MWAA Environment |

<!-- END_TF_DOCS -->
