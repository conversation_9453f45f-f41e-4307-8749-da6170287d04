# lambda function
resource "aws_lambda_function" "lambda_function_1" {
  function_name = "Query_Engine_Opensearch-1"
  timeout       = 900
  image_uri     = var.lambda_container_search
  package_type  = "Image"

  role = var.lambda_iam_role_arn

    environment {
    variables = {
      ENVIRONMENT = var.env_name
      numsearch   = "3"
    }
  }
}
resource "aws_lambda_function" "lambda_function_2" {
  function_name = "Opensearch_Collection_Creation_or_Deletion-1"
  timeout       = 900 
  image_uri     = var.lambda_container_collection
  package_type  = "Image"

  role =var.lambda_iam_role_arn

    environment {
    variables = {
      ENVIRONMENT = var.env_name
      numsearch   = "3"
    }
  }
}