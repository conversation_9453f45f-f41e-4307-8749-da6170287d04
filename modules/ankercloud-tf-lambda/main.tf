# Lambda Function for OpenSearch Query Engine
resource "aws_lambda_function" "search_function" {
  function_name = "${var.name_prefix}-${var.environment}-search-function"
  timeout       = var.lambda_timeout
  memory_size   = var.lambda_memory_size
  image_uri     = var.lambda_search_image_uri
  package_type  = "Image"
  role          = var.lambda_execution_role_arn

  environment {
    variables = merge(var.environment_variables, {
      ENVIRONMENT = var.environment
      numsearch   = var.search_num_results
    })
  }

  dynamic "vpc_config" {
    for_each = var.vpc_config != null ? [var.vpc_config] : []
    content {
      subnet_ids         = vpc_config.value.subnet_ids
      security_group_ids = vpc_config.value.security_group_ids
    }
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-search-function"
  })
}

# Lambda Function for OpenSearch Collection Management
resource "aws_lambda_function" "collection_function" {
  function_name = "${var.name_prefix}-${var.environment}-collection-function"
  timeout       = var.lambda_timeout
  memory_size   = var.lambda_memory_size
  image_uri     = var.lambda_collection_image_uri
  package_type  = "Image"
  role          = var.lambda_execution_role_arn

  environment {
    variables = merge(var.environment_variables, {
      ENVIRONMENT = var.environment
      numsearch   = var.search_num_results
    })
  }

  dynamic "vpc_config" {
    for_each = var.vpc_config != null ? [var.vpc_config] : []
    content {
      subnet_ids         = vpc_config.value.subnet_ids
      security_group_ids = vpc_config.value.security_group_ids
    }
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-collection-function"
  })
}
