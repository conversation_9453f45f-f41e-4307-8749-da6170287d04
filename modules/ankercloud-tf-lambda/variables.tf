
variable "lambda_container_search" {
  type = string
  description = "Lambda function container image"
  default = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_search_function:latest"
}

variable "lambda_container_collection" {
    type=string
    description = "Lambda collection creation function container image"
    default = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_collection_creation:latest"
  
}

variable "env_name" {
  description = "Environment name"
}

variable "lambda_iam_role_arn" {
    type = string
    description = "IAM role arn for lambda function"
    default = "arn:aws:iam::652989381321:role/service-role/pg-index-query-ankercloud-dev-role-oojxmin3"
  
}