
# Naming and Environment
variable "name_prefix" {
  type        = string
  description = "Prefix for naming Lambda resources"
  default     = "ankercloud"
}

variable "environment" {
  type        = string
  description = "Environment name (e.g., dev, staging, prod)"
  default     = "dev"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all Lambda resources"
  default     = {}
}

# Lambda Configuration
variable "lambda_search_image_uri" {
  type        = string
  description = "Container image URI for the search Lambda function"
}

variable "lambda_collection_image_uri" {
  type        = string
  description = "Container image URI for the collection management Lambda function"
}

variable "lambda_execution_role_arn" {
  type        = string
  description = "ARN of the IAM role for Lambda function execution"
}

variable "lambda_timeout" {
  type        = number
  description = "Timeout for Lambda functions in seconds"
  default     = 900
}

variable "lambda_memory_size" {
  type        = number
  description = "Memory size for Lambda functions in MB"
  default     = 512
}

# Environment Variables
variable "environment_variables" {
  type        = map(string)
  description = "Environment variables for Lambda functions"
  default     = {}
}

variable "search_num_results" {
  type        = string
  description = "Number of search results to return"
  default     = "3"
}

# VPC Configuration (optional)
variable "vpc_config" {
  type = object({
    subnet_ids         = list(string)
    security_group_ids = list(string)
  })
  description = "VPC configuration for Lambda functions (optional)"
  default     = null
}
