output "search_function_arn" {
  description = "ARN of the search Lambda function"
  value       = aws_lambda_function.search_function.arn
}

output "search_function_name" {
  description = "Name of the search Lambda function"
  value       = aws_lambda_function.search_function.function_name
}

output "search_function_invoke_arn" {
  description = "Invoke ARN of the search Lambda function"
  value       = aws_lambda_function.search_function.invoke_arn
}

output "collection_function_arn" {
  description = "ARN of the collection management Lambda function"
  value       = aws_lambda_function.collection_function.arn
}

output "collection_function_name" {
  description = "Name of the collection management Lambda function"
  value       = aws_lambda_function.collection_function.function_name
}

output "collection_function_invoke_arn" {
  description = "Invoke ARN of the collection management Lambda function"
  value       = aws_lambda_function.collection_function.invoke_arn
}

output "lambda_functions" {
  description = "Map of all Lambda functions with their details"
  value = {
    search = {
      arn        = aws_lambda_function.search_function.arn
      name       = aws_lambda_function.search_function.function_name
      invoke_arn = aws_lambda_function.search_function.invoke_arn
    }
    collection = {
      arn        = aws_lambda_function.collection_function.arn
      name       = aws_lambda_function.collection_function.function_name
      invoke_arn = aws_lambda_function.collection_function.invoke_arn
    }
  }
}
