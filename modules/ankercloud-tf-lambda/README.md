# AnkerCloud Terraform Lambda Module

This Terraform module creates AWS Lambda functions for AnkerCloud's OpenSearch operations. It includes functions for search queries and collection management.

## Features

- Lambda function for OpenSearch query engine
- Lambda function for OpenSearch collection management
- Configurable container images from ECR
- Environment variable support
- Optional VPC configuration
- Configurable timeout and memory settings
- Comprehensive tagging support

## Usage

```hcl
module "ankercloud_lambda" {
  source = "../modules/ankercloud-tf-lambda"

  # Naming and Environment
  name_prefix = "ankercloud"
  environment = "dev"

  # Lambda Configuration
  lambda_search_image_uri     = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_search_function:latest"
  lambda_collection_image_uri = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_collection_creation:latest"
  lambda_execution_role_arn   = aws_iam_role.lambda_execution_role.arn

  # Performance Configuration
  lambda_timeout    = 900
  lambda_memory_size = 1024

  # Environment Variables
  environment_variables = {
    OPENSEARCH_ENDPOINT = "https://search-domain.us-east-1.es.amazonaws.com"
    LOG_LEVEL          = "INFO"
  }

  search_num_results = "5"

  # VPC Configuration (optional)
  vpc_config = {
    subnet_ids         = ["subnet-12345678", "subnet-87654321"]
    security_group_ids = ["sg-12345678"]
  }

  # Tags
  tags = {
    Environment = "dev"
    Project     = "AnkerCloud"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version  |
| ------------------------------------------------------ | -------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0.0 |

## Modules

No modules.

## Resources

| Name                                                                                                                                   | Type     |
| -------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [aws_lambda_function.collection_function](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_function) | resource |
| [aws_lambda_function.search_function](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_function)     | resource |

## Inputs

| Name                                                                                                               | Description                                                       | Type                                                                                              | Default        | Required |
| ------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- | -------------- | :------: |
| <a name="input_environment"></a> [environment](#input_environment)                                                 | Environment name (e.g., dev, staging, prod)                       | `string`                                                                                          | `"dev"`        |    no    |
| <a name="input_environment_variables"></a> [environment_variables](#input_environment_variables)                   | Environment variables for Lambda functions                        | `map(string)`                                                                                     | `{}`           |    no    |
| <a name="input_lambda_collection_image_uri"></a> [lambda_collection_image_uri](#input_lambda_collection_image_uri) | Container image URI for the collection management Lambda function | `string`                                                                                          | n/a            |   yes    |
| <a name="input_lambda_execution_role_arn"></a> [lambda_execution_role_arn](#input_lambda_execution_role_arn)       | ARN of the IAM role for Lambda function execution                 | `string`                                                                                          | n/a            |   yes    |
| <a name="input_lambda_memory_size"></a> [lambda_memory_size](#input_lambda_memory_size)                            | Memory size for Lambda functions in MB                            | `number`                                                                                          | `512`          |    no    |
| <a name="input_lambda_search_image_uri"></a> [lambda_search_image_uri](#input_lambda_search_image_uri)             | Container image URI for the search Lambda function                | `string`                                                                                          | n/a            |   yes    |
| <a name="input_lambda_timeout"></a> [lambda_timeout](#input_lambda_timeout)                                        | Timeout for Lambda functions in seconds                           | `number`                                                                                          | `900`          |    no    |
| <a name="input_name_prefix"></a> [name_prefix](#input_name_prefix)                                                 | Prefix for naming Lambda resources                                | `string`                                                                                          | `"ankercloud"` |    no    |
| <a name="input_search_num_results"></a> [search_num_results](#input_search_num_results)                            | Number of search results to return                                | `string`                                                                                          | `"3"`          |    no    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                      | Tags to apply to all Lambda resources                             | `map(string)`                                                                                     | `{}`           |    no    |
| <a name="input_vpc_config"></a> [vpc_config](#input_vpc_config)                                                    | VPC configuration for Lambda functions (optional)                 | <pre>object({<br/> subnet_ids = list(string)<br/> security_group_ids = list(string)<br/> })</pre> | `null`         |    no    |

## Outputs

| Name                                                                                                                          | Description                                             |
| ----------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------- |
| <a name="output_collection_function_arn"></a> [collection_function_arn](#output_collection_function_arn)                      | ARN of the collection management Lambda function        |
| <a name="output_collection_function_invoke_arn"></a> [collection_function_invoke_arn](#output_collection_function_invoke_arn) | Invoke ARN of the collection management Lambda function |
| <a name="output_collection_function_name"></a> [collection_function_name](#output_collection_function_name)                   | Name of the collection management Lambda function       |
| <a name="output_lambda_functions"></a> [lambda_functions](#output_lambda_functions)                                           | Map of all Lambda functions with their details          |
| <a name="output_search_function_arn"></a> [search_function_arn](#output_search_function_arn)                                  | ARN of the search Lambda function                       |
| <a name="output_search_function_invoke_arn"></a> [search_function_invoke_arn](#output_search_function_invoke_arn)             | Invoke ARN of the search Lambda function                |
| <a name="output_search_function_name"></a> [search_function_name](#output_search_function_name)                               | Name of the search Lambda function                      |

<!-- END_TF_DOCS -->
