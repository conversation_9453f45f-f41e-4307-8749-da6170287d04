# tf-vpc

Deploy an AWS vpc

## Example

```terraform
module "vpc" {
  source = "./modules/vpc"

  # Flow Logs
  setup_vpc_flow_logs = true
  # Network config
  cidr_block = "10.5.0.0/16"

  netnum_shift_map = {
    public   = 0
    private  = 1
    isolated = 4
  }

  newbits = {
    public   = 8 # /24
    private  = 6 # /22
    isolated = 6 # /22
  }

  subnet_map = {
    public   = 3
    private  = 3
    isolated = 3
  }

  nat_instances = 3

  # Tagging
  tag_map_subnets = {
    public = {
      "Type" = "public"
    }
    private = {
      "Type" = "private"
    }
    isolated = {
      "Type" = "isolated"
    }
  }

  tag_map = {
    Name           = "acme"
    BillingProject = "AcmeCloud"
    Project        = "Base"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version |
| ------------------------------------------------------ | ------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                                       | Type        |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----------- |
| [aws_default_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_security_group)                                                      | resource    |
| [aws_eip.nat](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip)                                                                                             | resource    |
| [aws_flow_log.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/flow_log)                                                                                  | resource    |
| [aws_internet_gateway.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/internet_gateway)                                                                  | resource    |
| [aws_nat_gateway.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/nat_gateway)                                                                            | resource    |
| [aws_route.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route)                                                                                     | resource    |
| [aws_route.public](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route)                                                                                      | resource    |
| [aws_route_table.isolated](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table)                                                                        | resource    |
| [aws_route_table.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table)                                                                         | resource    |
| [aws_route_table.public](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table)                                                                          | resource    |
| [aws_route_table_association.isolated](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association)                                                | resource    |
| [aws_route_table_association.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association)                                                 | resource    |
| [aws_route_table_association.public](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association)                                                  | resource    |
| [aws_s3_bucket.flow_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket)                                                                           | resource    |
| [aws_s3_bucket_lifecycle_configuration.flow_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_lifecycle_configuration)                           | resource    |
| [aws_s3_bucket_metric.flow_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_metric)                                                             | resource    |
| [aws_s3_bucket_public_access_block.flow_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block)                                   | resource    |
| [aws_s3_bucket_server_side_encryption_configuration.flow_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource    |
| [aws_subnet.isolated](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet)                                                                                  | resource    |
| [aws_subnet.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet)                                                                                   | resource    |
| [aws_subnet.public](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet)                                                                                    | resource    |
| [aws_vpc.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc)                                                                                            | resource    |
| [aws_availability_zones.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/availability_zones)                                                           | data source |

## Inputs

| Name                                                                                       | Description                                                                              | Type                                                                                                       | Default                                                                             | Required |
| ------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------- | :------: |
| <a name="input_cidr_block"></a> [cidr_block](#input_cidr_block)                            | The CIDR block for the VPC                                                               | `string`                                                                                                   | `"10.0.0.0/16"`                                                                     |    no    |
| <a name="input_instance_tenancy"></a> [instance_tenancy](#input_instance_tenancy)          | The tenancy option for instances launched into the VPC (default, dedicated).             | `string`                                                                                                   | `"default"`                                                                         |    no    |
| <a name="input_nat_instances"></a> [nat_instances](#input_nat_instances)                   | The number of NAT gateways to create, must match or exceed the number of private subnets | `number`                                                                                                   | `3`                                                                                 |    no    |
| <a name="input_netnum_shift_map"></a> [netnum_shift_map](#input_netnum_shift_map)          | Specifies the offset for subnet numbering within the CIDR block                          | `map(number)`                                                                                              | <pre>{<br/> "isolated": 4,<br/> "private": 1,<br/> "public": 0<br/>}</pre>          |    no    |
| <a name="input_newbits"></a> [newbits](#input_newbits)                                     | Control the size of subnets (number of additional bits added to the VPC CIDR block)      | `map(number)`                                                                                              | <pre>{<br/> "isolated": 6,<br/> "private": 6,<br/> "public": 8<br/>}</pre>          |    no    |
| <a name="input_setup_vpc_flow_logs"></a> [setup_vpc_flow_logs](#input_setup_vpc_flow_logs) | Should an S3 bucket be setup, and have VPC flow logs be sent to it                       | `bool`                                                                                                     | `false`                                                                             |    no    |
| <a name="input_subnet_map"></a> [subnet_map](#input_subnet_map)                            | How many of each subnet kind                                                             | <pre>object({<br/> public = number<br/> private = number<br/> isolated = optional(number, 0)<br/> })</pre> | <pre>{<br/> "isolated": 3,<br/> "private": 3,<br/> "public": 3<br/>}</pre>          |    no    |
| <a name="input_subnet_prefix"></a> [subnet_prefix](#input_subnet_prefix)                   | Prefix CIDR blocks for public, private, and isolated subnets. Null if not predefined     | <pre>object({<br/> public = string<br/> private = string<br/> isolated = string<br/> })</pre>              | <pre>{<br/> "isolated": null,<br/> "private": null,<br/> "public": null<br/>}</pre> |    no    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                                     | A map of tags to assign to resources                                                     | `map(string)`                                                                                              | <pre>{<br/> "BillingProject": "FooBarBazCo",<br/> "Name": "Foo"<br/>}</pre>         |    no    |
| <a name="input_tag_map_subnets"></a> [tag_map_subnets](#input_tag_map_subnets)             | Additional tags for public, private, and isolated subnets                                | `map(map(string))`                                                                                         | <pre>{<br/> "isolated": {},<br/> "private": {},<br/> "public": {}<br/>}</pre>       |    no    |

## Outputs

| Name                                                                                                        | Description                                            |
| ----------------------------------------------------------------------------------------------------------- | ------------------------------------------------------ |
| <a name="output_default_security_group"></a> [default_security_group](#output_default_security_group)       | The default sg for the vpc that is created             |
| <a name="output_flow_logs_bucket"></a> [flow_logs_bucket](#output_flow_logs_bucket)                         | The S3 bucket that is created if flow logs are enabled |
| <a name="output_nat_gateway_eips"></a> [nat_gateway_eips](#output_nat_gateway_eips)                         | The Elastic IPs created for NAT gateways               |
| <a name="output_nat_gateways"></a> [nat_gateways](#output_nat_gateways)                                     | The NAT gateways that are created                      |
| <a name="output_route_table_associations"></a> [route_table_associations](#output_route_table_associations) | The route table associations that are made             |
| <a name="output_route_tables"></a> [route_tables](#output_route_tables)                                     | The route tables that are created                      |
| <a name="output_subnets"></a> [subnets](#output_subnets)                                                    | The subnets that are created                           |
| <a name="output_vpc"></a> [vpc](#output_vpc)                                                                | The VPC that is created                                |

<!-- END_TF_DOCS -->
