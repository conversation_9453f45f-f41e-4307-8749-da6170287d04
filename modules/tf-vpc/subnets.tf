locals {
  tag_name = contains(keys(var.tag_map), "Name") ? var.tag_map["Name"] : "default"
  tag_map_subnets = {
    public   = merge(var.tag_map, lookup(var.tag_map_subnets, "public", {}), { "Name" = join("-", [local.tag_name, "public"]) })
    private  = merge(var.tag_map, lookup(var.tag_map_subnets, "private", {}), { "Name" = join("-", [local.tag_name, "private"]) })
    isolated = merge(var.tag_map, lookup(var.tag_map_subnets, "isolated", {}), { "Name" = join("-", [local.tag_name, "isolated"]) })
  }
}

resource "aws_subnet" "public" {
  count = var.subnet_map["public"]

  availability_zone = length(regexall("^[a-z]{2}-", element(var.azs, count.index))) > 0 ? element(var.azs, count.index) : null
  cidr_block = cidrsubnet(
    var.subnet_prefix["public"] != null ? var.subnet_prefix["public"] : var.cidr_block,
    var.newbits["public"],
    var.netnum_shift_map["public"] + count.index
  )
  map_public_ip_on_launch = true
  vpc_id                  = aws_vpc.this.id
  tags                    = local.tag_map_subnets["public"]
}

resource "aws_subnet" "private" {
  count = var.subnet_map["private"]

  availability_zone = length(regexall("^[a-z]{2}-", element(var.azs, count.index))) > 0 ? element(var.azs, count.index) : null
  cidr_block = cidrsubnet(
    var.subnet_prefix["private"] != null ? var.subnet_prefix["private"] : var.cidr_block,
    var.newbits["private"],
    var.netnum_shift_map["private"] + count.index
  )
  vpc_id = aws_vpc.this.id
  tags   = local.tag_map_subnets["private"]
}

resource "aws_subnet" "isolated" {
  count = var.subnet_map["isolated"]

  availability_zone = length(regexall("^[a-z]{2}-", element(var.azs, count.index))) > 0 ? element(var.azs, count.index) : null
  cidr_block = cidrsubnet(
    var.subnet_prefix["isolated"] != null ? var.subnet_prefix["isolated"] : var.cidr_block,
    var.newbits["isolated"],
    var.netnum_shift_map["isolated"] + count.index
  )
  vpc_id = aws_vpc.this.id
  tags   = local.tag_map_subnets["isolated"]
}
