variable "cidr_block" {
  type        = string
  description = "The CIDR block for the VPC"
  default     = "10.0.0.0/16"
}

variable "instance_tenancy" {
  type        = string
  description = "The tenancy option for instances launched into the VPC (default, dedicated)."
  default     = "default"
}

variable "nat_instances" {
  type        = number
  description = "The number of NAT gateways to create, must match or exceed the number of private subnets"
  default     = 3
}

variable "netnum_shift_map" {
  type        = map(number)
  description = "Specifies the offset for subnet numbering within the CIDR block"
  default = {
    public   = 0
    private  = 1
    isolated = 4
  }
}

variable "newbits" {
  type        = map(number)
  description = "Control the size of subnets (number of additional bits added to the VPC CIDR block)"
  default = {
    public   = 8 # /24
    private  = 6 # /22
    isolated = 6 # /22
  }
}

variable "setup_vpc_flow_logs" {
  type        = bool
  description = "Should an S3 bucket be setup, and have VPC flow logs be sent to it"
  default     = false
}

variable "subnet_map" {
  type = object({
    public   = number
    private  = number
    isolated = optional(number, 0)
  })
  description = "How many of each subnet kind"
  default = {
    public   = 3
    private  = 3
    isolated = 3
  }
}

variable "subnet_prefix" {
  type = object({
    public   = string
    private  = string
    isolated = string
  })
  description = "Prefix CIDR blocks for public, private, and isolated subnets. Null if not predefined"
  default = {
    public   = null
    private  = null
    isolated = null
  }
}

variable "tag_map" {
  type        = map(string)
  description = "A map of tags to assign to resources"
  default = {
    Name           = "Foo"
    BillingProject = "FooBarBazCo"
  }
}

variable "tag_map_subnets" {
  type        = map(map(string))
  description = "Additional tags for public, private, and isolated subnets"
  default = {
    public   = {}
    private  = {}
    isolated = {}
  }
}

variable "azs" {
  description = "A list of availability zones names or ids in the region"
  type        = list(string)
  default     = []
}
