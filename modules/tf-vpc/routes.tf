locals {
  all_ipv4 = "0.0.0.0/0"
}

# Public
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.this.id
  tags   = local.tag_map_subnets["public"]
}

resource "aws_route" "public" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = local.all_ipv4
  gateway_id             = aws_internet_gateway.this.id
}

resource "aws_route_table_association" "public" {
  count = var.subnet_map["public"]

  subnet_id      = element(aws_subnet.public.*.id, count.index)
  route_table_id = aws_route_table.public.id
}

# Private
resource "aws_route_table" "private" {
  count = var.nat_instances

  vpc_id = aws_vpc.this.id
  tags   = local.tag_map_subnets["private"]
}

resource "aws_route" "private" {
  count = var.nat_instances

  destination_cidr_block = local.all_ipv4
  nat_gateway_id         = element(aws_nat_gateway.this.*.id, count.index)
  route_table_id         = element(aws_route_table.private.*.id, count.index)
}

resource "aws_route_table_association" "private" {
  count = var.subnet_map["public"]

  subnet_id      = element(aws_subnet.private.*.id, count.index)
  route_table_id = element(aws_route_table.private.*.id, count.index)
}

# Isolated
resource "aws_route_table" "isolated" {
  vpc_id = aws_vpc.this.id
  tags   = local.tag_map_subnets["isolated"]
}

resource "aws_route_table_association" "isolated" {
  count = var.subnet_map["isolated"]

  subnet_id      = element(aws_subnet.isolated.*.id, count.index)
  route_table_id = aws_route_table.isolated.id
}
