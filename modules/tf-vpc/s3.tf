resource "aws_s3_bucket" "flow_logs" {
  count         = var.setup_vpc_flow_logs ? 1 : 0
  bucket_prefix = "${local.tag_name}-vpc-flow-logs-"
  tags          = var.tag_map
}

resource "aws_s3_bucket_server_side_encryption_configuration" "flow_logs" {
  count  = var.setup_vpc_flow_logs ? 1 : 0
  bucket = aws_s3_bucket.flow_logs[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "flow_logs" {
  count                   = var.setup_vpc_flow_logs ? 1 : 0
  bucket                  = aws_s3_bucket.flow_logs[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}


resource "aws_s3_bucket_metric" "flow_logs" {
  count  = var.setup_vpc_flow_logs ? 1 : 0
  bucket = aws_s3_bucket.flow_logs[0].id
  name   = "EntireBucket"
}

resource "aws_s3_bucket_lifecycle_configuration" "flow_logs" {
  count  = var.setup_vpc_flow_logs ? 1 : 0
  bucket = aws_s3_bucket.flow_logs[0].id

  rule {
    id     = "delete-after-90-days"
    status = "Enabled"

    filter {
      prefix = ""
    }

    expiration {
      days = 90
    }
  }
}
