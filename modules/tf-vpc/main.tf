data "aws_availability_zones" "this" {
  state = "available"
}

resource "aws_vpc" "this" {
  cidr_block           = var.cidr_block
  enable_dns_support   = "true"
  enable_dns_hostnames = "true"
  instance_tenancy     = var.instance_tenancy
  tags                 = var.tag_map
}

resource "aws_internet_gateway" "this" {
  vpc_id = aws_vpc.this.id
  tags   = var.tag_map
}

resource "aws_default_security_group" "this" {
  vpc_id = aws_vpc.this.id
}

resource "aws_flow_log" "this" {
  count = var.setup_vpc_flow_logs ? 1 : 0

  log_destination      = aws_s3_bucket.flow_logs[0].arn
  log_destination_type = "s3"
  traffic_type         = "ALL"
  vpc_id               = aws_vpc.this.id
}

resource "aws_eip" "nat" {
  count  = var.nat_instances
  domain = "vpc"
  tags   = var.tag_map
}

resource "aws_nat_gateway" "this" {
  count      = var.nat_instances
  depends_on = [aws_internet_gateway.this]

  allocation_id = element(aws_eip.nat.*.id, count.index)
  subnet_id     = element(aws_subnet.public.*.id, count.index)
  tags          = var.tag_map
}
