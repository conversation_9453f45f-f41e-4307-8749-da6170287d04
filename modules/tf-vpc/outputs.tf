output "default_security_group" {
  description = "The default sg for the vpc that is created"
  value       = aws_default_security_group.this
}

output "nat_gateways" {
  description = "The NAT gateways that are created"
  value       = aws_nat_gateway.this[*]
}

output "nat_gateway_eips" {
  description = "The Elastic IPs created for NAT gateways"
  value       = aws_nat_gateway.this[*]
}

output "route_table_associations" {
  description = "The route table associations that are made"
  value = {
    public   = aws_route_table_association.public[*]
    private  = aws_route_table_association.private[*]
    isolated = aws_route_table_association.isolated[*]
  }
}

output "route_tables" {
  description = "The route tables that are created"
  value = {
    public   = aws_route_table.public[*]
    private  = aws_route_table.private[*]
    isolated = aws_route_table.isolated[*]
  }
}

output "subnets" {
  description = "The subnets that are created"
  value = {
    public   = aws_subnet.public[*]
    private  = aws_subnet.private[*]
    isolated = aws_subnet.isolated[*]
  }
}

output "vpc" {
  description = "The VPC that is created"
  value       = aws_vpc.this
}

output "flow_logs_bucket" {
  description = "The S3 bucket that is created if flow logs are enabled"
  value       = aws_s3_bucket.flow_logs
}
