locals {
  log_configs = {
    slow_index_logs         = { arn = aws_cloudwatch_log_group.log_groups["slow_index_logs"].arn, type = "INDEX_SLOW_LOGS" }
    slow_search_logs        = { arn = aws_cloudwatch_log_group.log_groups["slow_search_logs"].arn, type = "SEARCH_SLOW_LOGS" }
    opensearch_service_logs = { arn = aws_cloudwatch_log_group.log_groups["opensearch_service_logs"].arn, type = "ES_APPLICATION_LOGS" }
  }
  log_group_path_prefix = "/aws/opensearch/"
  log_groups = {
    slow_index_logs         = "${local.log_group_path_prefix}index-slow-logs/${var.domain_name}"
    slow_search_logs        = "${local.log_group_path_prefix}search-slow-logs/${var.domain_name}"
    opensearch_service_logs = "${local.log_group_path_prefix}application-logs/${var.domain_name}"
  }
}

resource "aws_cloudwatch_log_group" "log_groups" {
  for_each = local.log_groups

  name              = each.value
  retention_in_days = 90
  tags              = var.tag_map
}

resource "aws_cloudwatch_log_resource_policy" "this" {
  policy_name     = "AllowOpenSearchLogs"
  policy_document = data.aws_iam_policy_document.couldwatch_os_logs.json
}

data "aws_iam_policy_document" "couldwatch_os_logs" {
  statement {
    sid       = "AllowOpenSearchLogs"
    effect    = "Allow"
    resources = ["arn:aws:logs:*:${data.aws_caller_identity.current.account_id}:log-group:${local.log_group_path_prefix}*"]
    actions = [
      "logs:PutLogEvents",
      "logs:CreateLogStream"
    ]

    principals {
      type        = "Service"
      identifiers = ["es.amazonaws.com"]
    }
  }
}
