data "aws_caller_identity" "current" {}
data "aws_region" "current" {}
data "aws_vpc" "selected" {
  id = var.vpc_id
}

resource "aws_opensearch_domain" "this" {
  domain_name     = var.domain_name
  engine_version  = var.opensearch_version
  access_policies = data.aws_iam_policy_document.opensearch_access.json

  cluster_config {
    instance_type  = var.data_node_instance_type
    instance_count = var.data_node_instance_count

    zone_awareness_enabled = true
    zone_awareness_config {
      availability_zone_count = var.availability_zone_count
    }
  }

  domain_endpoint_options {
    enforce_https       = true
    tls_security_policy = "Policy-Min-TLS-1-2-PFS-2023-10"
  }

  ebs_options {
    ebs_enabled = true
    iops        = 3000
    volume_size = var.storage_gb
    volume_type = "gp3"
  }

  encrypt_at_rest {
    enabled = true
  }

  dynamic "log_publishing_options" {
    for_each = local.log_configs

    content {
      cloudwatch_log_group_arn = log_publishing_options.value.arn
      log_type                 = log_publishing_options.value.type
    }
  }

  node_to_node_encryption {
    enabled = true
  }

  vpc_options {
    subnet_ids         = var.subnet_ids
    security_group_ids = [aws_security_group.this.id]
  }

  tags = merge(var.tag_map, { Domain = var.domain_name })
}

data "aws_iam_policy_document" "opensearch_access" {
  statement {
    sid       = ""
    effect    = "Allow"
    resources = ["arn:aws:es:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:domain/${var.domain_name}/*"]
    actions   = ["es:*"]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
  }
}

resource "aws_iam_service_linked_role" "this" {
  count = var.create_service_linked_role ? 1 : 0

  aws_service_name = "opensearchservice.amazonaws.com"
  tags             = var.tag_map
}
