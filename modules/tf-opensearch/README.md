# tf-opensearch

## Example

```terraform
module "opensearch" {
  source = "./modules/opensearch"

  domain_name        = "foo-web-app"
  opensearch_version = "2.17"

  subnet_ids     = module.vpc.subnets.private[*].id
  vpc_cidr_block = module.vpc.vpc.cidr_block
  vpc_id         = module.vpc.vpc.id
  additional_ingress_cidrs = [
    "192.168.1.0/24",
    "10.100.0.0/16"
  ]

  data_node_instance_count = 3
  data_node_instance_type  = "m7g.large.search"
  storage_gb               = 15
  throughput_mb            = 125

  tag_map = {
    Name           = "OpenSearchWebApp"
    BillingProject = "WebApp"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version |
| ------------------------------------------------------ | ------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                  | Type        |
| ----------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_cloudwatch_log_group.log_groups](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group)               | resource    |
| [aws_cloudwatch_log_resource_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_resource_policy) | resource    |
| [aws_iam_service_linked_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_service_linked_role)               | resource    |
| [aws_opensearch_domain.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/opensearch_domain)                           | resource    |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)                                 | resource    |
| [aws_security_group_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule)                    | resource    |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity)                         | data source |
| [aws_iam_policy_document.couldwatch_os_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)      | data source |
| [aws_iam_policy_document.opensearch_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)       | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region)                                           | data source |
| [aws_vpc.selected](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/vpc)                                                | data source |

## Inputs

| Name                                                                                                            | Description                                                                                            | Type           | Default              | Required |
| --------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ | -------------- | -------------------- | :------: |
| <a name="input_additional_ingress_cidrs"></a> [additional_ingress_cidrs](#input_additional_ingress_cidrs)       | List of additional CIDR ranges allowed to access the OpenSearch domain                                 | `list(string)` | `[]`                 |    no    |
| <a name="input_availability_zone_count"></a> [availability_zone_count](#input_availability_zone_count)          | Number of availability zones for the cluster. Should match the number of subnets, valid values: 2 or 3 | `number`       | `3`                  |    no    |
| <a name="input_create_service_linked_role"></a> [create_service_linked_role](#input_create_service_linked_role) | Flag to indicate whether a service-linked role for OpenSearch should be created                        | `bool`         | `true`               |    no    |
| <a name="input_data_node_instance_count"></a> [data_node_instance_count](#input_data_node_instance_count)       | Number of data nodes to provision in the OpenSearch cluster                                            | `number`       | `3`                  |    no    |
| <a name="input_data_node_instance_type"></a> [data_node_instance_type](#input_data_node_instance_type)          | Instance type to use for the data nodes in the OpenSearch cluster                                      | `string`       | `"m7g.large.search"` |    no    |
| <a name="input_domain_name"></a> [domain_name](#input_domain_name)                                              | Name of the OpenSearch domain to be created                                                            | `string`       | n/a                  |   yes    |
| <a name="input_opensearch_version"></a> [opensearch_version](#input_opensearch_version)                         | Version of OpenSearch to deploy ('2.7', etc)                                                           | `string`       | n/a                  |   yes    |
| <a name="input_storage_gb"></a> [storage_gb](#input_storage_gb)                                                 | Amount of EBS storage (in GB) to allocate to each data node                                            | `number`       | `25`                 |    no    |
| <a name="input_subnet_ids"></a> [subnet_ids](#input_subnet_ids)                                                 | Set of subnet IDs where the OpenSearch cluster should be deployed                                      | `set(string)`  | n/a                  |   yes    |
| <a name="input_tag_map"></a> [tag_map](#input_tag_map)                                                          | Map of tags to apply to the created resources                                                          | `map(string)`  | n/a                  |   yes    |
| <a name="input_throughput_mb"></a> [throughput_mb](#input_throughput_mb)                                        | EBS throughput (in MB/s) for gp3 volumes attached to the data nodes                                    | `number`       | `125`                |    no    |
| <a name="input_vpc_cidr_block"></a> [vpc_cidr_block](#input_vpc_cidr_block)                                     | CIDR block of the VPC where the OpenSearch cluster will be deployed                                    | `string`       | n/a                  |   yes    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                             | ID of the VPC where the OpenSearch cluster will be deployed                                            | `string`       | n/a                  |   yes    |

## Outputs

| Name                                                                                         | Description                                |
| -------------------------------------------------------------------------------------------- | ------------------------------------------ |
| <a name="output_dashboards_endpoint"></a> [dashboards_endpoint](#output_dashboards_endpoint) | OpenSearch dashboards url                  |
| <a name="output_domain"></a> [domain](#output_domain)                                        | The OpenSearch domain that was created     |
| <a name="output_endpoint"></a> [endpoint](#output_endpoint)                                  | The OpenSearch endpoint without 'https://' |

<!-- END_TF_DOCS -->
