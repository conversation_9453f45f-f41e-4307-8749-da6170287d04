variable "additional_ingress_cidrs" {
  type        = list(string)
  description = "List of additional CIDR ranges allowed to access the OpenSearch domain"
  default     = []
}

variable "availability_zone_count" {
  type        = number
  description = "Number of availability zones for the cluster. Should match the number of subnets, valid values: 2 or 3"
  default     = 3
}

variable "create_service_linked_role" {
  type        = bool
  description = "Flag to indicate whether a service-linked role for OpenSearch should be created"
  default     = true
}

variable "data_node_instance_count" {
  type        = number
  description = "Number of data nodes to provision in the OpenSearch cluster"
  default     = 3
}

variable "data_node_instance_type" {
  type        = string
  description = "Instance type to use for the data nodes in the OpenSearch cluster"
  default     = "m7g.large.search"
}

variable "domain_name" {
  type        = string
  description = "Name of the OpenSearch domain to be created"
}

variable "opensearch_version" {
  type        = string
  description = "Version of OpenSearch to deploy ('2.7', etc)"
}

variable "storage_gb" {
  type        = number
  description = "Amount of EBS storage (in GB) to allocate to each data node"
  default     = 25
}

variable "subnet_ids" {
  type        = set(string)
  description = "Set of subnet IDs where the OpenSearch cluster should be deployed"
}

variable "tag_map" {
  type        = map(string)
  description = "Map of tags to apply to the created resources"
}

variable "throughput_mb" {
  type        = number
  description = "EBS throughput (in MB/s) for gp3 volumes attached to the data nodes"
  default     = 125
}

variable "vpc_cidr_block" {
  type        = string
  description = "CIDR block of the VPC where the OpenSearch cluster will be deployed"
}

variable "vpc_id" {
  type        = string
  description = "ID of the VPC where the OpenSearch cluster will be deployed"
}
