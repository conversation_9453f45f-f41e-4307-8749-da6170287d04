locals {
  port = 443
}

resource "aws_security_group" "this" {
  name   = "opensearch-${var.domain_name}"
  vpc_id = var.vpc_id
  tags   = var.tag_map
}

resource "aws_security_group_rule" "ingress" {
  for_each = toset(concat([var.vpc_cidr_block], var.additional_ingress_cidrs))

  security_group_id = aws_security_group.this.id
  type              = "ingress"
  from_port         = local.port
  to_port           = local.port
  protocol          = "tcp"
  cidr_blocks       = [each.value]
}
