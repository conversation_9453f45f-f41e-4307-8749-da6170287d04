locals {
  # Hostnames
  apex_host = var.name
  all_hosts = toset([local.apex_host, local.www_host])
  www_host  = "www.${local.apex_host}"
  # Policies
  cache_policies = {
    image_cache_policy = {
      name        = "ImageCachePolicy"
      comment     = "90 day cache policy for images"
      default_ttl = 7890000
      min_ttl     = 1
      max_ttl     = 31536000
    },
    default_cache_policy = {
      name        = "DefaultCachePolicy"
      comment     = "30 day cache policy"
      default_ttl = 2592000
      min_ttl     = 1
      max_ttl     = 31536000
    }
  }
  common_cache_parameters = {
    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true
    cookies_config = {
      cookie_behavior = "none"
    }
    headers_config = {
      header_behavior = "none"
    }
    query_strings_config = {
      query_string_behavior = "none"
    }
  }
  common_lambda_function_associations = [
    {
      event_type   = "viewer-request"
      lambda_arn   = aws_lambda_function.lambda_functions["www_to_non_www"].qualified_arn
      include_body = false
    },
    {
      event_type   = "viewer-response"
      lambda_arn   = aws_lambda_function.lambda_functions["modify_response_header"].qualified_arn
      include_body = false
    }
  ]
  default_additional_lambda_associations = [
    {
      event_type   = "origin-request"
      lambda_arn   = aws_lambda_function.lambda_functions["default_object"].qualified_arn
      include_body = false
    }
  ]
  default_lambda_function_associations = concat(
    local.common_lambda_function_associations,
    local.default_additional_lambda_associations
  )
  image_cache_lambda_function_associations = local.common_lambda_function_associations
  methods                                  = ["GET", "HEAD"]
  s3_origin_id                             = "${local.project}-S3Origin"
  viewer_protocol_policy                   = "redirect-to-https"
}

data "aws_cloudfront_origin_request_policy" "S3Origin" {
  name = "Managed-CORS-S3Origin"
}

resource "aws_cloudfront_origin_access_control" "oac" {
  name                              = local.project
  description                       = "${local.project} Origin Access Control policy"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_cloudfront_distribution" "cloudfront_distribution" {
  enabled             = true
  is_ipv6_enabled     = true
  aliases             = local.all_hosts
  comment             = "Site - ${var.name}"
  price_class         = "PriceClass_All"
  default_root_object = "index.html"
  http_version        = "http2"

  origin {
    domain_name              = aws_s3_bucket.origin_bucket.bucket_regional_domain_name
    origin_id                = local.s3_origin_id
    origin_access_control_id = aws_cloudfront_origin_access_control.oac.id
    connection_attempts      = 3
    connection_timeout       = 10
  }

  viewer_certificate {
    acm_certificate_arn      = var.acm_certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  custom_error_response {
    error_caching_min_ttl = "30"
    error_code            = "404"
    response_code         = "404"
    response_page_path    = "/404.html"
  }

  logging_config {
    include_cookies = false
    bucket          = aws_s3_bucket.cloudfront_logs_bucket.bucket_domain_name
    prefix          = "cf_logs"
  }

  default_cache_behavior {
    allowed_methods          = local.methods
    cached_methods           = local.methods
    target_origin_id         = local.s3_origin_id
    cache_policy_id          = aws_cloudfront_cache_policy.cache_policies["default_cache_policy"].id
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.S3Origin.id
    compress                 = true
    viewer_protocol_policy   = local.viewer_protocol_policy

    dynamic "lambda_function_association" {
      for_each = local.default_lambda_function_associations
      content {
        event_type   = lambda_function_association.value.event_type
        lambda_arn   = lambda_function_association.value.lambda_arn
        include_body = lambda_function_association.value.include_body
      }
    }
  }

  # Cache behavior with precedence 0 for images/*
  ordered_cache_behavior {
    path_pattern             = "images/*"
    allowed_methods          = local.methods
    cached_methods           = local.methods
    target_origin_id         = local.s3_origin_id
    cache_policy_id          = aws_cloudfront_cache_policy.cache_policies["image_cache_policy"].id
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.S3Origin.id
    compress                 = true
    viewer_protocol_policy   = local.viewer_protocol_policy

    dynamic "lambda_function_association" {
      for_each = local.image_cache_lambda_function_associations
      content {
        event_type   = lambda_function_association.value.event_type
        lambda_arn   = lambda_function_association.value.lambda_arn
        include_body = lambda_function_association.value.include_body
      }
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "blacklist"
      locations        = var.blacklisted_locations
    }
  }

  tags = {
    Name = var.name
  }
}

resource "aws_cloudfront_cache_policy" "cache_policies" {
  for_each = local.cache_policies

  name        = each.value.name
  comment     = each.value.comment
  default_ttl = each.value.default_ttl
  min_ttl     = each.value.min_ttl
  max_ttl     = each.value.max_ttl

  parameters_in_cache_key_and_forwarded_to_origin {
    enable_accept_encoding_brotli = local.common_cache_parameters.enable_accept_encoding_brotli
    enable_accept_encoding_gzip   = local.common_cache_parameters.enable_accept_encoding_gzip

    cookies_config {
      cookie_behavior = local.common_cache_parameters.cookies_config.cookie_behavior
    }

    headers_config {
      header_behavior = local.common_cache_parameters.headers_config.header_behavior
    }

    query_strings_config {
      query_string_behavior = local.common_cache_parameters.query_strings_config.query_string_behavior
    }
  }
}
