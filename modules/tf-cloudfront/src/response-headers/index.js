"use strict";

exports.handler = (event, context, callback) => {
  const response = event.Records[0].cf.response;
  const headers = response.headers;

  headers["strict-transport-security"] = [
    {
      key: "Strict-Transport-Security",
      value: "max-age=63072000",
    },
  ];
  headers["content-security-policy"] = [
    {
      key: "Content-Security-Policy",
      value:
        "default-src 'unsafe-inline' https: 'self' data:; upgrade-insecure-requests; frame-ancestors 'self'",
    },
  ];
  headers["x-content-type-options"] = [
    {
      key: "X-Content-Type-Options",
      value: "nosniff",
    },
  ];
  headers["x-frame-options"] = [
    {
      key: "X-Frame-Options",
      value: "DENY",
    },
  ];
  headers["x-xss-protection"] = [
    {
      key: "X-XSS-Protection",
      value: "1; mode=block",
    },
  ];
  headers["referrer-policy"] = [
    {
      key: "Referrer-Policy",
      value: "same-origin",
    },
  ];
  headers["permissions-policy"] = [
    {
      key: "Permissions-Policy",
      value: "sync-xhr=(self)",
    },
  ];
  // headers["content-language"] = [
  //   {
  //     key: "Content-Language",
  //     value: "en-US",
  //   },
  // ];

  //Remove some headers we don't care about
  delete response.headers["server"];
  delete response.headers["x-amz-request-id"];
  delete response.headers["x-amz-id-2"];
  delete response.headers["x-amz-server-side-encryption"];
  delete response.headers["x-amz-meta-codebuild-content-sha256"];
  delete response.headers["x-amz-meta-codebuild-buildarn"];
  delete response.headers["x-amz-meta-codebuild-content-md5"];

  callback(null, response);
};
