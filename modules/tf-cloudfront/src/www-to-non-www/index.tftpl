'use strict';

exports.handler = async (event) => {
    const request = event.Records[0].cf.request;

    if (request.headers.host[0].value === 'www.${name}') {
        return {
            status: '301',
            statusDescription: `Redirecting to apex domain`,
            headers: {
                location: [{
                    key: 'Location',
                    value: `https://${name}$${request.uri}`
                }]
            }
        };
    }
    return request;
};