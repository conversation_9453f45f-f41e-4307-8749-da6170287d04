# tf-cloudfront

## Example

```terraform
module "cloudfront" {
  source = "./modules/cloudfront"

  # TODO
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                               | Version |
| ------------------------------------------------------------------ | ------- |
| <a name="requirement_archive"></a> [archive](#requirement_archive) | >= 2.7  |
| <a name="requirement_aws"></a> [aws](#requirement_aws)             | >= 5.0  |
| <a name="requirement_random"></a> [random](#requirement_random)    | >= 3.6  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                                                    | Type        |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_cloudfront_cache_policy.cache_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_cache_policy)                                                       | resource    |
| [aws_cloudfront_distribution.cloudfront_distribution](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_distribution)                                              | resource    |
| [aws_cloudfront_origin_access_control.oac](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_origin_access_control)                                                | resource    |
| [aws_cloudwatch_log_group.lambda_logging](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group)                                                             | resource    |
| [aws_iam_role.lambda_edge_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                                                                   | resource    |
| [aws_iam_role_policy.iam-policy-for-lambda-edge](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy)                                                           | resource    |
| [aws_lambda_function.lambda_functions](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_function)                                                                     | resource    |
| [aws_s3_bucket.cloudfront_logs_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket)                                                                           | resource    |
| [aws_s3_bucket.origin_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket)                                                                                    | resource    |
| [aws_s3_bucket_acl.cloudfront_logs_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_acl)                                                                   | resource    |
| [aws_s3_bucket_acl.origin_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_acl)                                                                            | resource    |
| [aws_s3_bucket_cors_configuration.origin_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_cors_configuration)                                              | resource    |
| [aws_s3_bucket_lifecycle_configuration.cloudfront_logs_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_lifecycle_configuration)                           | resource    |
| [aws_s3_bucket_metric.logs_bucket-metrics](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_metric)                                                                | resource    |
| [aws_s3_bucket_metric.origin_bucket-metrics](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_metric)                                                              | resource    |
| [aws_s3_bucket_policy.origin_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy)                                                               | resource    |
| [aws_s3_bucket_public_access_block.s3_bucket_logs_access_block](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block)                              | resource    |
| [aws_s3_bucket_public_access_block.s3_bucket_origin_access_block](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block)                            | resource    |
| [aws_s3_bucket_server_side_encryption_configuration.cloudfront_logs_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource    |
| [aws_s3_bucket_server_side_encryption_configuration.origin_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration)          | resource    |
| [random_pet.random-name](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/pet)                                                                                            | resource    |
| [archive_file.lambda_default_object](https://registry.terraform.io/providers/hashicorp/archive/latest/docs/data-sources/file)                                                                           | data source |
| [archive_file.lambda_response_headers](https://registry.terraform.io/providers/hashicorp/archive/latest/docs/data-sources/file)                                                                         | data source |
| [archive_file.lambda_www_to_non_www](https://registry.terraform.io/providers/hashicorp/archive/latest/docs/data-sources/file)                                                                           | data source |
| [aws_canonical_user_id.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/canonical_user_id)                                                                       | data source |
| [aws_cloudfront_origin_request_policy.S3Origin](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/cloudfront_origin_request_policy)                                        | data source |
| [aws_iam_policy_document.assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                                        | data source |
| [aws_iam_policy_document.iam_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                                           | data source |
| [aws_iam_policy_document.s3_origin_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                                          | data source |

## Inputs

| Name                                                                                             | Description                                          | Type          | Default          | Required |
| ------------------------------------------------------------------------------------------------ | ---------------------------------------------------- | ------------- | ---------------- | :------: |
| <a name="input_acm_certificate_arn"></a> [acm_certificate_arn](#input_acm_certificate_arn)       | The ACM cert ARN to use with this distribution       | `string`      | n/a              |   yes    |
| <a name="input_blacklisted_locations"></a> [blacklisted_locations](#input_blacklisted_locations) | Black listed countries, see ISO 3166-1-alpha-2 codes | `set(string)` | `[]`             |    no    |
| <a name="input_name"></a> [name](#input_name)                                                    | Site name (fqdn)                                     | `string`      | `"example.tech"` |    no    |

## Outputs

| Name                                                                                                           | Description                        |
| -------------------------------------------------------------------------------------------------------------- | ---------------------------------- |
| <a name="output_CloudFrontDefaultDomain"></a> [CloudFrontDefaultDomain](#output_CloudFrontDefaultDomain)       | The CloudFront Default Domain Name |
| <a name="output_CloudFrontDistributionID"></a> [CloudFrontDistributionID](#output_CloudFrontDistributionID)    | The CloudFront Distribution ID     |
| <a name="output_CloudFrontLoggingS3Bucket"></a> [CloudFrontLoggingS3Bucket](#output_CloudFrontLoggingS3Bucket) | The CloudFront Logging S3 Bucket   |
| <a name="output_OriginS3Bucket"></a> [OriginS3Bucket](#output_OriginS3Bucket)                                  | The Origin S3 Bucket               |

<!-- END_TF_DOCS -->
