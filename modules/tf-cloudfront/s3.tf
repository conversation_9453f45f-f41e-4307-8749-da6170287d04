resource "random_pet" "random-name" {
  length = 3
}

# Origin bucket setup
resource "aws_s3_bucket" "origin_bucket" {

  bucket = "${local.project}-origin-${random_pet.random-name.id}"
  tags = {
    Name = "${var.name}-origin-bucket"
  }
}

resource "aws_s3_bucket_acl" "origin_bucket" {
  bucket = aws_s3_bucket.origin_bucket.bucket
  acl    = "private"
}

resource "aws_s3_bucket_cors_configuration" "origin_bucket" {
  bucket = aws_s3_bucket.origin_bucket.bucket
  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = ["*"]
    expose_headers  = []
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "origin_bucket" {
  bucket = aws_s3_bucket.origin_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "s3_bucket_origin_access_block" {
  bucket                  = aws_s3_bucket.origin_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

data "aws_iam_policy_document" "s3_origin_policy" {
  statement {
    sid       = "AllowCloudFrontServicePrincipalReadOnly"
    effect    = "Allow"
    resources = ["${aws_s3_bucket.origin_bucket.arn}/*"]
    actions   = ["s3:GetObject"]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = [aws_cloudfront_distribution.cloudfront_distribution.arn]
    }

    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
  }
}

resource "aws_s3_bucket_policy" "origin_bucket_policy" {
  bucket = aws_s3_bucket.origin_bucket.id
  policy = data.aws_iam_policy_document.s3_origin_policy.json
}

resource "aws_s3_bucket_metric" "origin_bucket-metrics" {
  bucket = aws_s3_bucket.origin_bucket.bucket
  name   = "EntireBucket"
}

# CloudFront Logging Bucket
data "aws_canonical_user_id" "current" {}
resource "aws_s3_bucket" "cloudfront_logs_bucket" {
  bucket = "${local.project}-logs-${random_pet.random-name.id}"
  tags = {
    Name = "${var.name}-CloudFront-Logs-Bucket"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "cloudfront_logs_bucket" {
  bucket = aws_s3_bucket.cloudfront_logs_bucket.bucket
  rule {
    id = "log"
    expiration {
      days = 90
    }
    filter {
      prefix = "cf_logs/"
    }
    status = "Enabled"
  }
}

resource "aws_s3_bucket_acl" "cloudfront_logs_bucket" {
  bucket = aws_s3_bucket.cloudfront_logs_bucket.bucket

  access_control_policy {
    grant {
      grantee {
        id   = data.aws_canonical_user_id.current.id
        type = "CanonicalUser"
      }
      permission = "FULL_CONTROL"
    }
    grant {
      grantee {
        id   = "c4c1ede66af53448b93c283ce9448c4ba468c9432aa01d700d3878632f77d2d0"
        type = "CanonicalUser"
      }
      permission = "FULL_CONTROL"
    }
    owner {
      id = data.aws_canonical_user_id.current.id
    }
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "cloudfront_logs_bucket" {
  bucket = aws_s3_bucket.cloudfront_logs_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "s3_bucket_logs_access_block" {
  bucket                  = aws_s3_bucket.cloudfront_logs_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_metric" "logs_bucket-metrics" {
  bucket = aws_s3_bucket.cloudfront_logs_bucket.bucket
  name   = "EntireBucket"
}
