locals {
  arch              = ["x86_64"]
  cf_lambda_runtime = "nodejs20.x"
  handler           = "index.handler"
  lambda_functions = {
    www_to_non_www = {
      description      = "CF Apex Redirect"
      filename         = data.archive_file.lambda_www_to_non_www.output_path
      source_code_hash = data.archive_file.lambda_www_to_non_www.output_base64sha256
    }
    modify_response_header = {
      description      = "CF Response Headers"
      filename         = data.archive_file.lambda_response_headers.output_path
      source_code_hash = data.archive_file.lambda_response_headers.output_base64sha256
    }
    default_object = {
      description      = "CF Default Object"
      filename         = data.archive_file.lambda_default_object.output_path
      source_code_hash = data.archive_file.lambda_default_object.output_base64sha256
    }
  }
  lambda_log_groups = {
    www_to_non_www         = aws_lambda_function.lambda_functions["www_to_non_www"].function_name
    modify_response_header = aws_lambda_function.lambda_functions["modify_response_header"].function_name
    default_object         = aws_lambda_function.lambda_functions["default_object"].function_name
  }
}

data "archive_file" "lambda_www_to_non_www" {
  type = "zip"
  source {
    content = templatefile(
      "${path.module}/src/www-to-non-www/index.tftpl",
      { name = var.name }
    )
    filename = "index.js"
  }
  output_path = "dist/www-to-non-www/lambda.zip"
}

data "archive_file" "lambda_response_headers" {
  type        = "zip"
  source_dir  = "${path.module}/src/response-headers"
  output_path = "dist/response-headers/lambda.zip"
}

data "archive_file" "lambda_default_object" {
  type        = "zip"
  source_dir  = "${path.module}/src/default-object"
  output_path = "dist/default-object/lambda.zip"
}

resource "aws_lambda_function" "lambda_functions" {
  for_each = local.lambda_functions

  function_name    = "${local.project}-${each.key}"
  description      = each.value.description
  filename         = each.value.filename
  package_type     = "Zip"
  source_code_hash = each.value.source_code_hash
  role             = aws_iam_role.lambda_edge_role.arn
  handler          = local.handler
  runtime          = local.cf_lambda_runtime
  architectures    = local.arch
  memory_size      = 128
  publish          = true

  ephemeral_storage {
    size = 512
  }
}

resource "aws_cloudwatch_log_group" "lambda_logging" {
  for_each = local.lambda_log_groups

  name              = "/aws/lambda/us-east-1.${each.value}"
  retention_in_days = 1
}


data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    effect    = "Allow"
    actions   = ["sts:AssumeRole"]
    resources = []
    principals {
      type = "Service"
      identifiers = [
        "edgelambda.amazonaws.com",
        "lambda.amazonaws.com"
      ]
    }
  }
}

data "aws_iam_policy_document" "iam_role_policy" {
  statement {
    effect  = "Allow"
    actions = ["logs:CreateLogStream", "logs:PutLogEvents"]

    resources = flatten([
      for log_group in aws_cloudwatch_log_group.lambda_logging : "${log_group.arn}:*"
    ])
  }
}

resource "aws_iam_role" "lambda_edge_role" {
  name_prefix        = "cloudfront_lambda_role-"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy" "iam-policy-for-lambda-edge" {
  role   = aws_iam_role.lambda_edge_role.name
  policy = data.aws_iam_policy_document.iam_role_policy.json
}
