data "aws_iam_policy_document" "ec2_assume_role" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}
# IAM Role for ECS Instance
resource "aws_iam_role" "ecs_instance_role" {
  name               = "${var.name_prefix}-${var.environment}-ecs-instance-role"
  assume_role_policy = data.aws_iam_policy_document.ec2_assume_role.json

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-ecs-instance-role"
  })
}

resource "aws_iam_role_policy_attachment" "ecs_instance_role" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_role_policy_attachment" "ecs_s3_full_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_policy" "opensearch_access_policy" {
  name        = "${var.name_prefix}-${var.environment}-opensearch-access"
  description = "Policy for full access to OpenSearch collections"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid      = "OpenSearchFullAccess",
        Effect   = "Allow",
        Action   = "aoss:*",
        Resource = "*"
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "ecs_opensearch_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = aws_iam_policy.opensearch_access_policy.arn
}

resource "aws_iam_policy" "bedrock_access_policy" {
  name        = "${var.name_prefix}-${var.environment}-bedrock-access"
  description = "Policy for full access to AWS Bedrock"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "bedrock:*",
        Resource = "*"
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "ecs_bedrock_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = aws_iam_policy.bedrock_access_policy.arn
}

resource "aws_iam_policy" "batch_access_policy" {
  name        = "${var.name_prefix}-${var.environment}-batch-access"
  description = "Policy for full access to AWS Batch jobs"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "batch:*",
        Resource = "*"
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "ecs_batch_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = aws_iam_policy.batch_access_policy.arn
}

resource "aws_iam_instance_profile" "ecs_instance_role" {
  name = "${var.name_prefix}-${var.environment}-ecs-instance-profile"
  role = aws_iam_role.ecs_instance_role.name

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-ecs-instance-profile"
  })
}

data "aws_iam_policy_document" "batch_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["batch.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "aws_batch_service_role" {
  name               = "${var.name_prefix}-${var.environment}-batch-service-role"
  assume_role_policy = data.aws_iam_policy_document.batch_assume_role.json

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-batch-service-role"
  })
}

resource "aws_iam_role_policy_attachment" "aws_batch_service_role" {
  role       = aws_iam_role.aws_batch_service_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBatchServiceRole"
}

# Security Group for Batch Compute Environment
resource "aws_security_group" "batch_compute_sg" {
  name        = "${var.name_prefix}-${var.environment}-batch-compute-sg"
  description = "Security group for AWS Batch compute environment"
  vpc_id      = var.vpc_id

  ingress {
    description = "SSH access"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.security_group_cidr_blocks
  }

  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-batch-compute-sg"
  })
}

# VPC Data Source
data "aws_vpc" "existing_vpc" {
  id = var.vpc_id
}

# Subnet Data Sources
data "aws_subnets" "batch_subnets" {
  filter {
    name   = "subnet-id"
    values = var.subnet_ids
  }
}

# Placement Group for Batch Compute Environment
resource "aws_placement_group" "batch_placement_group" {
  name     = "${var.name_prefix}-${var.environment}-batch-placement-group"
  strategy = "cluster"

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-batch-placement-group"
  })
}

# AWS Batch Compute Environment
resource "aws_batch_compute_environment" "batch_compute_env" {
  type = "MANAGED"

  compute_resources {
    instance_role = aws_iam_instance_profile.ecs_instance_role.arn
    instance_type = var.batch_instance_types
    max_vcpus     = var.batch_max_vcpus
    min_vcpus     = var.batch_min_vcpus
    type          = "EC2"

    placement_group = aws_placement_group.batch_placement_group.name

    security_group_ids = [
      aws_security_group.batch_compute_sg.id,
    ]

    subnets = var.subnet_ids

    tags = var.tags
  }

  service_role = aws_iam_role.aws_batch_service_role.arn
  depends_on   = [aws_iam_role_policy_attachment.aws_batch_service_role]

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-batch-compute-env"
  })
}

# AWS Batch Job Definition
resource "aws_batch_job_definition" "batch_job_definition" {
  name = "${var.name_prefix}-${var.environment}-job-definition"
  type = "container"

  platform_capabilities = ["EC2"]

  container_properties = jsonencode({
    image   = var.batch_container_image
    command = var.batch_job_definition_command

    resourceRequirements = [
      {
        type  = "VCPU"
        value = var.job_vcpu
      },
      {
        type  = "MEMORY"
        value = var.job_memory
      }
    ]

    volumes     = []
    environment = []
    mountPoints = []
    ulimits     = []
    secrets     = []

    linuxParameters = {
      devices = []
      tmpfs   = []
    }
  })

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-job-definition"
  })
}

# AWS Batch Job Queue
resource "aws_batch_job_queue" "batch_job_queue" {
  name     = "${var.name_prefix}-${var.environment}-job-queue"
  state    = "ENABLED"
  priority = 1

  compute_environment_order {
    order               = 1
    compute_environment = aws_batch_compute_environment.batch_compute_env.arn
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.environment}-job-queue"
  })
}

