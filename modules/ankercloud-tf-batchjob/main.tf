data "aws_iam_policy_document" "ec2_assume_role" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}
#iam role
resource "aws_iam_role" "ecs_instance_role" {
  name               = "ecsInstanceRole"
  assume_role_policy = data.aws_iam_policy_document.ec2_assume_role.json
}

resource "aws_iam_role_policy_attachment" "ecs_instance_role" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_role_policy_attachment" "ecs_s3_full_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_policy" "opensearch_access_policy" {
  name        = "OpenSearchFullAccess"
  description = "Policy for full access to OpenSearch collections"
  
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid       = "OpenSearchFullAccess",
        Effect    = "Allow",
        Action    = "aoss:*",
        Resource  = "*"
      }
    ]
  })
}
resource "aws_iam_role_policy_attachment" "ecs_opensearch_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = aws_iam_policy.opensearch_access_policy.arn
}

resource "aws_iam_policy" "bedrock_access_policy" {
  name        = "BedrockFullAccess"
  description = "Policy for full access to AWS Bedrock"
  
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect    = "Allow",
        Action    = "bedrock:*",
        Resource  = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_bedrock_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = aws_iam_policy.bedrock_access_policy.arn
}
#iam policy which will be attached to the ecs instance role
resource "aws_iam_policy" "batch_access_policy" {
  name        = "BatchFullAccess"
  description = "Policy for full access to AWS Batch jobs"
  
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect    = "Allow",
        Action    = "batch:*",
        Resource  = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_batch_access" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = aws_iam_policy.batch_access_policy.arn
}

resource "aws_iam_instance_profile" "ecs_instance_role" {
  name = "ecs_instance_role"
  role = aws_iam_role.ecs_instance_role.name
}

data "aws_iam_policy_document" "batch_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["batch.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "aws_batch_service_role" {
  name               = "aws_batch_service_role"
  assume_role_policy = data.aws_iam_policy_document.batch_assume_role.json
}

resource "aws_iam_role_policy_attachment" "aws_batch_service_role" {
  role       = aws_iam_role.aws_batch_service_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBatchServiceRole"
}

#security group
resource "aws_security_group" "sample" {
  name = "aws_batch_compute_environment_security_gro    up"
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.security_group_cidr_block]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.security_group_cidr_block]
  }
}
#vpc

data "aws_vpc" "existing_vpc" {
  filter {
    name   = "vpc-id"
    values = [var.vpc_id]
  }
}

#subnet
data "aws_subnet" "existing_subnet" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.existing_vpc.id]
  }
  filter {
    name="cidr-block"
    values=[var.cidr_block]
  }
}

#it decides what type of cluster should be created in ecs 1. cluster-created closer.
resource "aws_placement_group" "sample" {
  name     = "sample"
  strategy = "cluster"
}

#aws batch compute env, instance type,docker image,min,max cpu,memory are defined here which will be used in the batch job definition.
resource "aws_batch_compute_environment" "sample" {
  compute_environment_name = "sample"

  compute_resources {
    instance_role = aws_iam_instance_profile.ecs_instance_role.arn

    instance_type = var.aws_batch_jobs_instance_type

    max_vcpus = 16
    min_vcpus = 0

    placement_group = aws_placement_group.sample.name

    security_group_ids = [
      aws_security_group.sample.id,
    ]

    subnets = [
      data.aws_subnet.existing_subnet.id,
    ]

    type = "EC2"
  }

  service_role = aws_iam_role.aws_batch_service_role.arn
  type         = "MANAGED"
  depends_on   = [aws_iam_role_policy_attachment.aws_batch_service_role]
}

#aws batch job definition.
resource "aws_batch_job_definition" "test" {
  name = "getting-started-ec2-job-definition"
  type = "container"

  platform_capabilities = ["EC2"]

  container_properties = jsonencode({
    image   = var.batch_container_image
    command = var.batch_job_definition_command

    resourceRequirements = [
      {
        type  = "VCPU"
        value = "1"
      },
      {
        type  = "MEMORY"
        value = "2048"
      }
    ]

    volumes      = []
    environment  = []
    mountPoints  = []
    ulimits      = []
    secrets      = []

    linuxParameters = {
      devices = []
      tmpfs   = []
    }
  })
}

#aws batch queue.
resource "aws_batch_job_queue" "test_queue" {
  name     = "tf-test-batch-job-queue"
  state    = "ENABLED"
  priority = 1

  compute_environment_order {
    order               = 1
    compute_environment = aws_batch_compute_environment.sample.arn
  }
}

