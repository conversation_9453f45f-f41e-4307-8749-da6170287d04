variable "vpc_id" {
  type        = string
  description = "Existing VPC ID"
  default     = "vpc-07addaa572ca5da3e"
}

variable "cidr_block" {
  type        = string
  description = "The CIDR block for the VPC"
  default     = "***********/20"
}

variable "security_group_cidr_block" {
    type = string
    description = "AWS Security Group CIDR range"
    default = "0.0.0.0/0"
  
}

variable "batch_container_image" {
  type=string
  description="docker container image for batch jobs"
  default = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_batch_jobs:new"
}

variable "aws_batch_jobs_instance_type" {
    type = list(string)
    description = "Batch job instance type that will be used when a job is submitted"
    default = ["c4.large","c4.xlarge","c4.2xlarge","c4.4xlarge"] 
}

variable "batch_job_definition_command" {
    type = list(string)
    description = "Batch Job Definition container command that is executed when a conatiner is run"
    default = ["--s3_uri","s3://ankercloud-bucket-poc/"]
  
}