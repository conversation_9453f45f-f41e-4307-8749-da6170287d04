# Network Configuration
variable "vpc_id" {
  type        = string
  description = "ID of the existing VPC where batch resources will be deployed"
}

variable "subnet_ids" {
  type        = list(string)
  description = "List of subnet IDs where batch compute environment will be deployed"
}

variable "security_group_cidr_blocks" {
  type        = list(string)
  description = "List of CIDR blocks allowed to access the batch compute environment"
  default     = ["10.0.0.0/8"]
}

# Naming and Tagging
variable "name_prefix" {
  type        = string
  description = "Prefix for naming batch resources"
  default     = "ankercloud"
}

variable "environment" {
  type        = string
  description = "Environment name (e.g., dev, staging, prod)"
  default     = "dev"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all batch resources"
  default     = {}
}

# Batch Configuration
variable "batch_container_image" {
  type        = string
  description = "Docker container image URI for batch jobs"
}

variable "batch_job_definition_command" {
  type        = list(string)
  description = "Default command to execute when batch container runs"
  default     = []
}

variable "batch_instance_types" {
  type        = list(string)
  description = "List of EC2 instance types for batch compute environment"
  default     = ["c5.large", "c5.xlarge", "c5.2xlarge", "c5.4xlarge"]
}

variable "batch_max_vcpus" {
  type        = number
  description = "Maximum number of vCPUs for the batch compute environment"
  default     = 16
}

variable "batch_min_vcpus" {
  type        = number
  description = "Minimum number of vCPUs for the batch compute environment"
  default     = 0
}

# Job Definition Configuration
variable "job_vcpu" {
  type        = string
  description = "Number of vCPUs for the job definition"
  default     = "1"
}

variable "job_memory" {
  type        = string
  description = "Amount of memory (in MB) for the job definition"
  default     = "2048"
}
