# AnkerCloud Terraform Batch Job Module

This Terraform module creates AWS Batch resources for running containerized batch jobs. It includes compute environments, job definitions, job queues, and the necessary IAM roles and policies.

## Features

- AWS Batch compute environment with EC2 instances
- Configurable job definition with container specifications
- Job queue for managing batch job execution
- IAM roles and policies for batch operations
- Security group for compute environment
- Support for OpenSearch, Bedrock, and S3 access
- Configurable instance types and resource limits

## Usage

```hcl
module "ankercloud_batch" {
  source = "../modules/ankercloud-tf-batchjob"

  # Network Configuration
  vpc_id     = "vpc-12345678"
  subnet_ids = ["subnet-12345678", "subnet-87654321"]

  # Naming and Environment
  name_prefix = "ankercloud"
  environment = "dev"

  # Batch Configuration
  batch_container_image = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_batch_jobs:latest"
  batch_instance_types  = ["c5.large", "c5.xlarge"]
  batch_max_vcpus      = 32
  batch_min_vcpus      = 0

  # Job Configuration
  job_vcpu   = "2"
  job_memory = "4096"

  # Tags
  tags = {
    Environment = "dev"
    Project     = "AnkerCloud"
  }
}
```

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                   | Version  |
| ------------------------------------------------------ | -------- |
| <a name="requirement_aws"></a> [aws](#requirement_aws) | >= 5.0.0 |

## Modules

No modules.

## Resources

| Name                                                                                                                                                            | Type        |
| --------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_batch_compute_environment.batch_compute_env](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/batch_compute_environment)        | resource    |
| [aws_batch_job_definition.batch_job_definition](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/batch_job_definition)               | resource    |
| [aws_batch_job_queue.batch_job_queue](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/batch_job_queue)                              | resource    |
| [aws_iam_instance_profile.ecs_instance_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile)                  | resource    |
| [aws_iam_policy.batch_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy)                                    | resource    |
| [aws_iam_policy.bedrock_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy)                                  | resource    |
| [aws_iam_policy.opensearch_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy)                               | resource    |
| [aws_iam_role.aws_batch_service_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                     | resource    |
| [aws_iam_role.ecs_instance_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role)                                          | resource    |
| [aws_iam_role_policy_attachment.aws_batch_service_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource    |
| [aws_iam_role_policy_attachment.ecs_batch_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)       | resource    |
| [aws_iam_role_policy_attachment.ecs_bedrock_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)     | resource    |
| [aws_iam_role_policy_attachment.ecs_instance_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)      | resource    |
| [aws_iam_role_policy_attachment.ecs_opensearch_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)  | resource    |
| [aws_iam_role_policy_attachment.ecs_s3_full_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment)     | resource    |
| [aws_placement_group.batch_placement_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/placement_group)                        | resource    |
| [aws_security_group.batch_compute_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group)                               | resource    |
| [aws_iam_policy_document.batch_assume_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                 | data source |
| [aws_iam_policy_document.ec2_assume_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                   | data source |
| [aws_subnets.batch_subnets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnets)                                             | data source |
| [aws_vpc.existing_vpc](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/vpc)                                                      | data source |

## Inputs

| Name                                                                                                                  | Description                                                         | Type           | Default                                                                                    | Required |
| --------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------- | -------------- | ------------------------------------------------------------------------------------------ | :------: |
| <a name="input_batch_container_image"></a> [batch_container_image](#input_batch_container_image)                      | Docker container image URI for batch jobs                           | `string`       | n/a                                                                                        |   yes    |
| <a name="input_batch_instance_types"></a> [batch_instance_types](#input_batch_instance_types)                         | List of EC2 instance types for batch compute environment            | `list(string)` | <pre>[<br/> "c5.large",<br/> "c5.xlarge",<br/> "c5.2xlarge",<br/> "c5.4xlarge"<br/>]</pre> |    no    |
| <a name="input_batch_job_definition_command"></a> [batch_job_definition_command](#input_batch_job_definition_command) | Default command to execute when batch container runs                | `list(string)` | `[]`                                                                                       |    no    |
| <a name="input_batch_max_vcpus"></a> [batch_max_vcpus](#input_batch_max_vcpus)                                        | Maximum number of vCPUs for the batch compute environment           | `number`       | `16`                                                                                       |    no    |
| <a name="input_batch_min_vcpus"></a> [batch_min_vcpus](#input_batch_min_vcpus)                                        | Minimum number of vCPUs for the batch compute environment           | `number`       | `0`                                                                                        |    no    |
| <a name="input_environment"></a> [environment](#input_environment)                                                    | Environment name (e.g., dev, staging, prod)                         | `string`       | `"dev"`                                                                                    |    no    |
| <a name="input_job_memory"></a> [job_memory](#input_job_memory)                                                       | Amount of memory (in MB) for the job definition                     | `string`       | `"2048"`                                                                                   |    no    |
| <a name="input_job_vcpu"></a> [job_vcpu](#input_job_vcpu)                                                             | Number of vCPUs for the job definition                              | `string`       | `"1"`                                                                                      |    no    |
| <a name="input_name_prefix"></a> [name_prefix](#input_name_prefix)                                                    | Prefix for naming batch resources                                   | `string`       | `"ankercloud"`                                                                             |    no    |
| <a name="input_security_group_cidr_blocks"></a> [security_group_cidr_blocks](#input_security_group_cidr_blocks)       | List of CIDR blocks allowed to access the batch compute environment | `list(string)` | <pre>[<br/> "10.0.0.0/8"<br/>]</pre>                                                       |    no    |
| <a name="input_subnet_ids"></a> [subnet_ids](#input_subnet_ids)                                                       | List of subnet IDs where batch compute environment will be deployed | `list(string)` | n/a                                                                                        |   yes    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                         | Tags to apply to all batch resources                                | `map(string)`  | `{}`                                                                                       |    no    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                                   | ID of the existing VPC where batch resources will be deployed       | `string`       | n/a                                                                                        |   yes    |

## Outputs

| Name                                                                                                                          | Description                                            |
| ----------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------ |
| <a name="output_batch_compute_environment_arn"></a> [batch_compute_environment_arn](#output_batch_compute_environment_arn)    | ARN of the AWS Batch compute environment               |
| <a name="output_batch_compute_environment_name"></a> [batch_compute_environment_name](#output_batch_compute_environment_name) | Name of the AWS Batch compute environment              |
| <a name="output_batch_job_definition_arn"></a> [batch_job_definition_arn](#output_batch_job_definition_arn)                   | ARN of the AWS Batch job definition                    |
| <a name="output_batch_job_definition_name"></a> [batch_job_definition_name](#output_batch_job_definition_name)                | Name of the AWS Batch job definition                   |
| <a name="output_batch_job_queue_arn"></a> [batch_job_queue_arn](#output_batch_job_queue_arn)                                  | ARN of the AWS Batch job queue                         |
| <a name="output_batch_job_queue_name"></a> [batch_job_queue_name](#output_batch_job_queue_name)                               | Name of the AWS Batch job queue                        |
| <a name="output_batch_service_role_arn"></a> [batch_service_role_arn](#output_batch_service_role_arn)                         | ARN of the AWS Batch service role                      |
| <a name="output_ecs_instance_role_arn"></a> [ecs_instance_role_arn](#output_ecs_instance_role_arn)                            | ARN of the ECS instance role                           |
| <a name="output_security_group_id"></a> [security_group_id](#output_security_group_id)                                        | ID of the security group for batch compute environment |

<!-- END_TF_DOCS -->
