output "tfstate_bucket" {
  value       = aws_s3_bucket.terraform_state.bucket
  description = "The name of the S3 bucket"
}

output "tfstate_bucket_arn" {
  value       = aws_s3_bucket.terraform_state.arn
  description = "The ARN of the S3 bucket"
}

output "tfstate_bucket_region" {
  value       = aws_s3_bucket.terraform_state.region
  description = "The region of the S3 bucket"
}

output "tfstate_dynamodb_table_name" {
  value       = aws_dynamodb_table.terraform_state_lock.name
  description = "The name of the DynamoDB table"
}

output "tfstate_dynamodb_table_arn" {
  value       = aws_dynamodb_table.terraform_state_lock.arn
  description = "The ARN of the DynamoDB table"
}
