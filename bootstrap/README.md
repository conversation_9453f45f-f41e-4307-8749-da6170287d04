# Bootstrap

This is the very beginning, where we will store Terraform state.

- 🟢 Run `terraform init`, `terraform plan`, `terraform apply`
- 🟢 Uncomment everything in backend.tf, and run `terraform init -migrate-state`

<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                                     | Version  |
| ------------------------------------------------------------------------ | -------- |
| <a name="requirement_terraform"></a> [terraform](#requirement_terraform) | ~> 1.9   |
| <a name="requirement_aws"></a> [aws](#requirement_aws)                   | >= 5.0.0 |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                                                 | Type        |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_dynamodb_table.terraform_state_lock](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/dynamodb_table)                                                                | resource    |
| [aws_s3_bucket.terraform_state](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket)                                                                               | resource    |
| [aws_s3_bucket_policy.terraform_state_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy)                                                          | resource    |
| [aws_s3_bucket_public_access_block.terraform_state_access_block](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block)                          | resource    |
| [aws_s3_bucket_server_side_encryption_configuration.terraform_state_sse](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource    |
| [aws_s3_bucket_versioning.terraform_state_versioning](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_versioning)                                              | resource    |
| [aws_iam_policy_document.terraform_state_https_only](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document)                                             | data source |

## Inputs

No inputs.

## Outputs

| Name                                                                                                                 | Description                    |
| -------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| <a name="output_tfstate_bucket"></a> [tfstate_bucket](#output_tfstate_bucket)                                        | The name of the S3 bucket      |
| <a name="output_tfstate_bucket_arn"></a> [tfstate_bucket_arn](#output_tfstate_bucket_arn)                            | The ARN of the S3 bucket       |
| <a name="output_tfstate_bucket_region"></a> [tfstate_bucket_region](#output_tfstate_bucket_region)                   | The region of the S3 bucket    |
| <a name="output_tfstate_dynamodb_table_arn"></a> [tfstate_dynamodb_table_arn](#output_tfstate_dynamodb_table_arn)    | The ARN of the DynamoDB table  |
| <a name="output_tfstate_dynamodb_table_name"></a> [tfstate_dynamodb_table_name](#output_tfstate_dynamodb_table_name) | The name of the DynamoDB table |

<!-- END_TF_DOCS -->
