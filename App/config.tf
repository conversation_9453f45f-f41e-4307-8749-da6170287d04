resource "aws_secretsmanager_secret" "pg_service_config" {
  name        = "pg-services-config-secrets"
  description = "Config for PG Services"
}

resource "aws_secretsmanager_secret_version" "pg_service_config" {
  secret_id = aws_secretsmanager_secret.pg_service_config.id
  secret_string = jsonencode({
    neptune = {
      url        = module.neptune.neptune_cluster_endpoint
      reader_url = module.neptune.neptune_cluster_reader_endpoint
      port       = module.neptune.neptune_cluster_port
    },
    opensearch = {
      endpoint = "https://${module.opensearch.endpoint}"
    }
  })
}

resource "aws_secretsmanager_secret" "dm_pg_service_config" {
  name        = "dm-pg-services-config-secrets"
  description = "Config for DM PG Services"
}

resource "aws_secretsmanager_secret_version" "dm_pg_service_config" {
  secret_id = aws_secretsmanager_secret.dm_pg_service_config.id
  secret_string = jsonencode({
    neptune = {
      url        = module.dm_neptune.neptune_cluster_endpoint
      reader_url = module.dm_neptune.neptune_cluster_reader_endpoint
      port       = module.dm_neptune.neptune_cluster_port
    },
    opensearch = {
      endpoint = "https://${module.dm_opensearch.endpoint}"
    },
    ankercloud-pipelines = {
      opensearch_collection_endpoint = module.dm_ankercloud_opensearch.collection_endpoint
      opensearch_dashboard_endpoint  = module.dm_ankercloud_opensearch.dashboard_endpoint
      search_lambda_arn              = module.dm_ankercloud_lambda.search_function_arn
      collection_lambda_arn          = module.dm_ankercloud_lambda.collection_function_arn
      batch_job_queue_arn            = module.dm_ankercloud_batch.batch_job_queue_arn
      batch_job_definition_arn       = module.dm_ankercloud_batch.batch_job_definition_arn
    }
  })
}
