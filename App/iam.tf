resource "aws_iam_role" "app_ecr_reader_role" {
  name = "app-ecr-reader-role"

  assume_role_policy = data.aws_iam_policy_document.app_ecr_reader_trust.json
}

resource "aws_iam_role_policy_attachment" "app_ecr_reader_attachment" {
  role       = aws_iam_role.app_ecr_reader_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSAppRunnerServicePolicyForECRAccess"
}

resource "aws_iam_role" "dm_app_ecr_reader_role" {
  name = "dm-app-ecr-reader-role"

  assume_role_policy = data.aws_iam_policy_document.app_ecr_reader_trust.json
}

resource "aws_iam_role_policy_attachment" "dm_app_ecr_reader_attachment" {
  role       = aws_iam_role.dm_app_ecr_reader_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSAppRunnerServicePolicyForECRAccess"
}

data "aws_iam_policy_document" "app_ecr_reader_trust" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["build.apprunner.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "app_runner_role" {
  name = "app-runner-role"

  assume_role_policy = data.aws_iam_policy_document.app_runner_trust.json
}

resource "aws_iam_role_policy_attachment" "app_runner_memorydb" {
  role       = aws_iam_role.app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonMemoryDBFullAccess"
}

resource "aws_iam_role_policy_attachment" "app_runner_sqs" {
  role       = aws_iam_role.app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSQSFullAccess"
}

resource "aws_iam_role_policy_attachment" "app_runner_neptune" {
  role       = aws_iam_role.app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/NeptuneFullAccess"
}

resource "aws_iam_role_policy_attachment" "app_runner_secrets" {
  role       = aws_iam_role.app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
}

resource "aws_iam_role" "dm_app_runner_role" {
  name = "dm-app-runner-role"

  assume_role_policy = data.aws_iam_policy_document.app_runner_trust.json
}

resource "aws_iam_role_policy_attachment" "dm_app_runner_memorydb" {
  role       = aws_iam_role.dm_app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonMemoryDBFullAccess"
}

resource "aws_iam_role_policy_attachment" "dm_app_runner_sqs" {
  role       = aws_iam_role.dm_app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSQSFullAccess"
}

resource "aws_iam_role_policy_attachment" "dm_app_runner_neptune" {
  role       = aws_iam_role.dm_app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/NeptuneFullAccess"
}

resource "aws_iam_role_policy_attachment" "dm_app_runner_secrets" {
  role       = aws_iam_role.dm_app_runner_role.name
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
}

data "aws_iam_policy_document" "app_runner_trust" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["tasks.apprunner.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}
