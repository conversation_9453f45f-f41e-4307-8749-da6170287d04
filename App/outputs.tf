output "service_endpoints" {
  value = { for service_name, service in aws_apprunner_service.pg_services :
  service_name => service.service_url }
  description = "The endpoints for each App Runner service"
}

output "dm_service_endpoints" {
  value = { for service_name, service in aws_apprunner_service.dm_pg_services :
  service_name => service.service_url }
  description = "The endpoints for each DM App Runner service"
}

# AnkerCloud outputs for dm environment
output "dm_ankercloud_opensearch_collection_endpoint" {
  value       = module.dm_ankercloud_opensearch.collection_endpoint
  description = "OpenSearch Serverless collection endpoint for AnkerCloud dm environment"
}

output "dm_ankercloud_opensearch_dashboard_endpoint" {
  value       = module.dm_ankercloud_opensearch.dashboard_endpoint
  description = "OpenSearch Serverless dashboard endpoint for AnkerCloud dm environment"
}

output "dm_ankercloud_lambda_functions" {
  value       = module.dm_ankercloud_lambda.lambda_functions
  description = "AnkerCloud Lambda functions for dm environment"
}

output "dm_ankercloud_batch_job_queue" {
  value       = module.dm_ankercloud_batch.batch_job_queue_name
  description = "AnkerCloud Batch job queue name for dm environment"
}

output "dm_ankercloud_batch_job_definition" {
  value       = module.dm_ankercloud_batch.batch_job_definition_name
  description = "AnkerCloud Batch job definition name for dm environment"
}
