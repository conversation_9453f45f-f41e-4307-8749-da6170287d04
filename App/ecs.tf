resource "aws_ecs_cluster" "pg_cluster" {
  name = "perception-grid-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = {
    Name           = "PerceptionGrid-ECS-Cluster"
    BillingProject = "PerceptionGrid"
    Project        = "PerceptionGridBase"
  }
}

resource "aws_ecs_cluster_capacity_providers" "pg_cluster_capacity" {
  cluster_name = aws_ecs_cluster.pg_cluster.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    capacity_provider = "FARGATE"
    weight            = 1
    base              = 1
  }
}

data "aws_iam_policy_document" "ecs_task_execution_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name               = "ecs-task-execution-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_execution_assume_role.json
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

data "aws_iam_policy_document" "ecs_task_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "ecs_task_role" {
  name               = "ecs-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_assume_role.json
}

# Attach necessary policies for the ECS tasks
# resource "aws_iam_role_policy_attachment" "ecs_task_role_memorydb" {
#   role       = aws_iam_role.ecs_task_role.name
#   policy_arn = "arn:aws:iam::aws:policy/AmazonMemoryDBFullAccess"
# }

resource "aws_iam_role_policy_attachment" "ecs_task_role_sqs" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSQSFullAccess"
}

# Neptune IAM auth is disabled, so this policy is not needed
# resource "aws_iam_role_policy_attachment" "ecs_task_role_neptune" {
#   role       = aws_iam_role.ecs_task_role.name
#   policy_arn = "arn:aws:iam::aws:policy/NeptuneFullAccess"
# }

resource "aws_iam_role_policy_attachment" "ecs_task_role_secrets" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
}

resource "aws_security_group" "alb_sg" {
  name        = "ecs-alb-sg"
  description = "Security group for ECS ALB"
  vpc_id      = module.vpc.vpc.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "ecs-alb-sg"
  }
}

resource "aws_security_group" "ecs_tasks_sg" {
  name        = "ecs-tasks-sg"
  description = "Security group for ECS tasks"
  vpc_id      = module.vpc.vpc.id

  ingress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.alb_sg.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "ecs-tasks-sg"
  }
}

resource "aws_lb" "ecs_alb" {
  name               = "ecs-alb"
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = module.vpc.subnets.private[*].id

  enable_deletion_protection = false

  tags = {
    Name = "ecs-alb"
  }
}

data "aws_acm_certificate" "pg_io" {
  domain      = "*.perceptiongrid.io"
  statuses    = ["ISSUED"]
  most_recent = true
}

resource "aws_lb_listener" "http" {
  load_balancer_arn = aws_lb.ecs_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "https" {
  load_balancer_arn = aws_lb.ecs_alb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = data.aws_acm_certificate.pg_io.arn

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Not Found"
      status_code  = "404"
    }
  }
}

# ECS Task Definitions and Services
locals {
  ecs_services = yamldecode(file("${path.module}/apps.yaml")).services
}

resource "aws_cloudwatch_log_group" "ecs_logs" {
  for_each = { for service in local.ecs_services : service.service_name => service }

  name              = "/ecs/${each.value.service_name}"
  retention_in_days = 30

  tags = {
    Name = "/ecs/${each.value.service_name}"
  }
}

resource "aws_ecs_task_definition" "service_task_definitions" {
  for_each = { for service in local.ecs_services : service.service_name => service }

  family                   = each.value.service_name
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "1024"
  memory                   = "2048"
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name      = each.value.service_name
      image     = each.value.image_identifier
      essential = true

      portMappings = [
        {
          containerPort = tonumber(each.value.image_port)
          hostPort      = tonumber(each.value.image_port)
          protocol      = "tcp"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/${each.value.service_name}"
          "awslogs-region"        = "us-east-1"
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = {
    Name = each.value.service_name
  }
}

resource "aws_lb_target_group" "service_target_groups" {
  for_each = { for service in local.ecs_services : service.service_name => service }

  name        = "${substr(each.value.service_name, 0, 26)}-tg"
  port        = tonumber(each.value.image_port)
  protocol    = "HTTP"
  vpc_id      = module.vpc.vpc.id
  target_type = "ip"

  health_check {
    enabled             = true
    interval            = each.value.health_check_interval
    port                = each.value.image_port
    protocol            = "HTTP"
    timeout             = each.value.health_check_timeout
    healthy_threshold   = 2 # Minimum value required
    unhealthy_threshold = each.value.unhealthy_threshold

    matcher = "200,404"
  }

  tags = {
    Name = "${each.value.service_name}-tg"
  }
}

resource "aws_lb_listener_rule" "service_rules" {
  for_each = { for service in local.ecs_services : service.service_name => service }

  listener_arn = aws_lb_listener.https.arn
  priority     = 100 + index(local.ecs_services.*.service_name, each.value.service_name)

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.service_target_groups[each.value.service_name].arn
  }

  condition {
    path_pattern {
      values = ["${each.value.path}/*"]
    }
  }
}

resource "aws_ecs_service" "services" {
  for_each = { for service in local.ecs_services : service.service_name => service }

  name            = each.value.service_name
  cluster         = aws_ecs_cluster.pg_cluster.id
  task_definition = aws_ecs_task_definition.service_task_definitions[each.value.service_name].arn
  desired_count   = 1
  launch_type     = "FARGATE"

  network_configuration {
    subnets          = module.vpc.subnets.private[*].id
    security_groups  = [aws_security_group.ecs_tasks_sg.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.service_target_groups[each.value.service_name].arn
    container_name   = each.value.service_name
    container_port   = tonumber(each.value.image_port)
  }

  depends_on = [
    aws_lb_listener.http,
    aws_lb_listener.https
  ]

  tags = {
    Name = each.value.service_name
  }
}
