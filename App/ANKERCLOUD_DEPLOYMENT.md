# AnkerCloud Modules Deployment Guide

This guide provides instructions for deploying the AnkerCloud modules (batch, lambda, and opensearch) to the dm_ environment only.

## Overview

The AnkerCloud integration includes:
- **OpenSearch Serverless**: Collection for search and analytics
- **Lambda Functions**: Search and collection management functions
- **Batch Jobs**: Containerized batch processing capabilities
- **IAM Roles & Policies**: Proper permissions for all services

## Prerequisites

1. Ensure you're in the `App/` directory
2. Terraform is initialized (`terraform init`)
3. AWS credentials are configured
4. Existing infrastructure is deployed and stable

## Deployment Steps

### Step 1: Plan the Deployment

First, review what will be created:

```bash
terraform plan -target=module.dm_ankercloud_opensearch \
               -target=module.dm_ankercloud_lambda \
               -target=module.dm_ankercloud_batch \
               -target=aws_iam_role.dm_ankercloud_lambda_execution_role \
               -target=aws_iam_policy.dm_ankercloud_opensearch_access \
               -target=aws_iam_policy.dm_ankercloud_s3_access \
               -target=aws_iam_policy.dm_ankercloud_batch_access \
               -target=aws_security_group.dm_ankercloud_lambda_sg
```

### Step 2: Deploy IAM Resources First

Deploy IAM roles and policies first to ensure proper permissions:

```bash
terraform apply -target=aws_iam_role.dm_ankercloud_lambda_execution_role \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_basic_execution \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_vpc_execution \
                -target=aws_iam_policy.dm_ankercloud_opensearch_access \
                -target=aws_iam_policy.dm_ankercloud_s3_access \
                -target=aws_iam_policy.dm_ankercloud_batch_access \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_opensearch_access \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_s3_access \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_batch_access \
                -target=aws_security_group.dm_ankercloud_lambda_sg
```

### Step 3: Deploy OpenSearch Serverless

Deploy the OpenSearch Serverless collection:

```bash
terraform apply -target=module.dm_ankercloud_opensearch
```

### Step 4: Deploy Lambda Functions

Deploy the Lambda functions (depends on OpenSearch for environment variables):

```bash
terraform apply -target=module.dm_ankercloud_lambda
```

### Step 5: Deploy Batch Resources

Deploy the Batch compute environment, job definition, and queue:

```bash
terraform apply -target=module.dm_ankercloud_batch
```

### Step 6: Update Configuration Secrets

Update the secrets manager with AnkerCloud endpoints:

```bash
terraform apply -target=aws_secretsmanager_secret_version.dm_pg_service_config
```

## Alternative: Single Command Deployment

If you prefer to deploy everything at once (after reviewing the plan):

```bash
terraform apply -target=aws_iam_role.dm_ankercloud_lambda_execution_role \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_basic_execution \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_vpc_execution \
                -target=aws_iam_policy.dm_ankercloud_opensearch_access \
                -target=aws_iam_policy.dm_ankercloud_s3_access \
                -target=aws_iam_policy.dm_ankercloud_batch_access \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_opensearch_access \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_s3_access \
                -target=aws_iam_role_policy_attachment.dm_ankercloud_lambda_batch_access \
                -target=aws_security_group.dm_ankercloud_lambda_sg \
                -target=module.dm_ankercloud_opensearch \
                -target=module.dm_ankercloud_lambda \
                -target=module.dm_ankercloud_batch \
                -target=aws_secretsmanager_secret_version.dm_pg_service_config
```

## Verification

After deployment, verify the resources:

### Check OpenSearch Collection
```bash
aws opensearchserverless list-collections --query 'collectionSummaries[?name==`dm-ankercloud-collection`]'
```

### Check Lambda Functions
```bash
aws lambda list-functions --query 'Functions[?starts_with(FunctionName, `dm-ankercloud`)]'
```

### Check Batch Resources
```bash
aws batch describe-job-queues --job-queues dm-ankercloud-dm-job-queue
aws batch describe-job-definitions --job-definition-name dm-ankercloud-dm-job-definition
```

### Check Outputs
```bash
terraform output dm_ankercloud_opensearch_collection_endpoint
terraform output dm_ankercloud_lambda_functions
terraform output dm_ankercloud_batch_job_queue
```

## Troubleshooting

### Common Issues

1. **IAM Permission Errors**: Ensure IAM resources are deployed first
2. **VPC Configuration**: Verify VPC and subnet IDs are correct
3. **Container Images**: Ensure ECR images exist and are accessible
4. **OpenSearch Policies**: Check that access policies include correct principals

### Rollback

To remove AnkerCloud resources:

```bash
terraform destroy -target=module.dm_ankercloud_batch \
                  -target=module.dm_ankercloud_lambda \
                  -target=module.dm_ankercloud_opensearch \
                  -target=aws_iam_role.dm_ankercloud_lambda_execution_role \
                  -target=aws_iam_policy.dm_ankercloud_opensearch_access \
                  -target=aws_iam_policy.dm_ankercloud_s3_access \
                  -target=aws_iam_policy.dm_ankercloud_batch_access \
                  -target=aws_security_group.dm_ankercloud_lambda_sg
```

## Notes

- Only the dm_ environment is configured for AnkerCloud modules
- All resources are properly tagged for identification
- Security groups and IAM policies follow least privilege principles
- Container images should be updated as needed in the configuration
