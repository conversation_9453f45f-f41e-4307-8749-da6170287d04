# App Networking
resource "aws_apprunner_auto_scaling_configuration_version" "default_pg" {
  auto_scaling_configuration_name = "default-pg"
  max_concurrency                 = 100
  min_size                        = 1
  max_size                        = 25

  tags = {
    Name = "default-pg"
  }
}

resource "aws_apprunner_auto_scaling_configuration_version" "dm_default_pg" {
  auto_scaling_configuration_name = "dm-default-pg"
  max_concurrency                 = 100
  min_size                        = 1
  max_size                        = 25

  tags = {
    Name = "dm-default-pg"
  }
}

resource "aws_security_group" "app_runner_sg" {
  name        = "app-runner-sg"
  description = "Security group for App Runner VPC access"
  vpc_id      = module.vpc.vpc.id

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "ALL"
    cidr_blocks = ["10.0.0.0/16"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "ALL"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "dm_app_runner_sg" {
  name        = "dm-app-runner-sg"
  description = "Security group for DM App Runner VPC access"
  vpc_id      = module.vpc.vpc.id

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "ALL"
    cidr_blocks = ["10.0.0.0/16"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "ALL"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_apprunner_vpc_connector" "app_runner_vpc_connector" {
  vpc_connector_name = "PGAppRunnerVpcConnector"
  subnets            = module.vpc.subnets.private[*].id
  security_groups    = [aws_security_group.app_runner_sg.id]
  tags = {
    Name = "PGAppRunnerVpcConnector"
  }
}

resource "aws_apprunner_vpc_connector" "dm_app_runner_vpc_connector" {
  vpc_connector_name = "DMPGAppRunnerVpcConnector"
  subnets            = module.vpc.subnets.private[*].id
  security_groups    = [aws_security_group.dm_app_runner_sg.id]
  tags = {
    Name = "DMPGAppRunnerVpcConnector"
  }
}

# Apps
locals {
  common_config = {
    network_configuration = {
      ingress_configuration = {
        is_publicly_accessible = true
      }
      egress_configuration = {
        egress_type       = "VPC"
        vpc_connector_arn = aws_apprunner_vpc_connector.app_runner_vpc_connector.arn
      }
    }
    access_role_arn                = aws_iam_role.app_ecr_reader_role.arn
    instance_role_arn              = aws_iam_role.app_runner_role.arn
    auto_scaling_configuration_arn = aws_apprunner_auto_scaling_configuration_version.default_pg.arn
  }

  dm_common_config = {
    network_configuration = {
      ingress_configuration = {
        is_publicly_accessible = true
      }
      egress_configuration = {
        egress_type       = "VPC"
        vpc_connector_arn = aws_apprunner_vpc_connector.dm_app_runner_vpc_connector.arn
      }
    }
    access_role_arn                = aws_iam_role.dm_app_ecr_reader_role.arn
    instance_role_arn              = aws_iam_role.dm_app_runner_role.arn
    auto_scaling_configuration_arn = aws_apprunner_auto_scaling_configuration_version.dm_default_pg.arn
  }

  services = yamldecode(file("${path.module}/apps.yaml")).services
}

resource "aws_apprunner_service" "pg_services" {
  for_each = { for service in local.services : service.service_name => service }

  service_name = each.value.service_name

  health_check_configuration {
    protocol            = each.value.health_check_protocol
    path                = each.value.health_check_path
    interval            = each.value.health_check_interval
    timeout             = each.value.health_check_timeout
    healthy_threshold   = each.value.healthy_threshold
    unhealthy_threshold = each.value.unhealthy_threshold
  }

  instance_configuration {
    cpu               = 1024
    memory            = 2048
    instance_role_arn = local.common_config.instance_role_arn
  }

  source_configuration {
    authentication_configuration {
      access_role_arn = local.common_config.access_role_arn
    }
    image_repository {
      image_configuration {
        port = each.value.image_port
      }
      image_identifier      = each.value.image_identifier
      image_repository_type = "ECR"
    }
    auto_deployments_enabled = false
  }

  network_configuration {
    ingress_configuration {
      is_publicly_accessible = local.common_config.network_configuration.ingress_configuration.is_publicly_accessible
    }
    egress_configuration {
      egress_type       = local.common_config.network_configuration.egress_configuration.egress_type
      vpc_connector_arn = local.common_config.network_configuration.egress_configuration.vpc_connector_arn
    }
  }

  auto_scaling_configuration_arn = local.common_config.auto_scaling_configuration_arn

  tags = {
    Name = each.value.service_name
  }
}

resource "aws_apprunner_service" "dm_pg_services" {
  for_each = { for service in local.services : service.service_name => service }

  service_name = "dm-${each.value.service_name}"

  health_check_configuration {
    protocol            = each.value.health_check_protocol
    path                = each.value.health_check_path
    interval            = each.value.health_check_interval
    timeout             = each.value.health_check_timeout
    healthy_threshold   = each.value.healthy_threshold
    unhealthy_threshold = each.value.unhealthy_threshold
  }

  instance_configuration {
    cpu               = 1024
    memory            = 2048
    instance_role_arn = local.dm_common_config.instance_role_arn
  }

  source_configuration {
    authentication_configuration {
      access_role_arn = local.dm_common_config.access_role_arn
    }
    image_repository {
      image_configuration {
        port = each.value.image_port
      }
      image_identifier      = each.value.image_identifier
      image_repository_type = "ECR"
    }
    auto_deployments_enabled = false
  }

  network_configuration {
    ingress_configuration {
      is_publicly_accessible = local.dm_common_config.network_configuration.ingress_configuration.is_publicly_accessible
    }
    egress_configuration {
      egress_type       = local.dm_common_config.network_configuration.egress_configuration.egress_type
      vpc_connector_arn = local.dm_common_config.network_configuration.egress_configuration.vpc_connector_arn
    }
  }

  auto_scaling_configuration_arn = local.dm_common_config.auto_scaling_configuration_arn

  tags = {
    Name = "dm-${each.value.service_name}"
  }
}
