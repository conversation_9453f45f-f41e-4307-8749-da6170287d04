data "aws_availability_zones" "available" {}
module "vpc" {
  source = "../modules/tf-vpc"

  # Flow Logs
  setup_vpc_flow_logs = false
  # Network config
  azs        = slice(data.aws_availability_zones.available.names, 0, 3)
  cidr_block = "10.5.0.0/16"

  netnum_shift_map = {
    public   = 0
    private  = 1
    isolated = 4
  }

  newbits = {
    public   = 8 # /24
    private  = 6 # /22
    isolated = 6 # /22
  }

  subnet_map = {
    public   = 3
    private  = 3
    isolated = 3
  }

  nat_instances = 3

  # Tagging
  tag_map_subnets = {
    public = {
      "Type" = "public"
    }
    private = {
      "Type" = "private"
    }
    isolated = {
      "Type" = "isolated"
    }
  }

  tag_map = {
    Name           = "PerceptionGrid"
    BillingProject = "PerceptionGrid"
    Project        = "PerceptionGridBase"
  }
}
