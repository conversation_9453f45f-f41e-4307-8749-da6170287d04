module "opensearch" {
  source = "../modules/tf-opensearch"

  domain_name        = "pg-app"
  opensearch_version = "OpenSearch_2.17"

  subnet_ids                 = module.vpc.subnets.private[*].id
  vpc_cidr_block             = module.vpc.vpc.cidr_block
  vpc_id                     = module.vpc.vpc.id
  additional_ingress_cidrs   = []
  create_service_linked_role = false

  data_node_instance_count = 3
  data_node_instance_type  = "m7g.medium.search"
  storage_gb               = 15
  throughput_mb            = 125

  tag_map = {
    Name           = "pg-app"
    BillingProject = "PerceptionGrid"
  }
}

module "dm_opensearch" {
  source = "../modules/tf-opensearch"

  domain_name        = "dm-pg-app"
  opensearch_version = "OpenSearch_2.17"

  subnet_ids                 = module.vpc.subnets.private[*].id
  vpc_cidr_block             = module.vpc.vpc.cidr_block
  vpc_id                     = module.vpc.vpc.id
  additional_ingress_cidrs   = []
  create_service_linked_role = false

  data_node_instance_count = 3
  data_node_instance_type  = "m7g.medium.search"
  storage_gb               = 15
  throughput_mb            = 125

  tag_map = {
    Name           = "dm-pg-app"
    BillingProject = "PerceptionGrid"
  }
}
