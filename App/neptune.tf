module "neptune" {
  source = "../modules/tf-neptune"

  cluster_identifier                  = "pg-cluster"
  availability_zones                  = slice(data.aws_availability_zones.available.names, 0, 3)
  instance_class                      = "db.t4g.medium"
  instance_count                      = 2
  engine_version                      = "*******"
  iam_database_authentication_enabled = false
  preferred_backup_window             = "03:00-04:00"
  skip_final_snapshot                 = true
  deletion_protection                 = false
  export_logs_to_cloudwatch           = ["audit", "slowquery"]
  log_retention_in_days               = 7

  vpc_cidr_block           = module.vpc.vpc.cidr_block
  vpc_id                   = module.vpc.vpc.id
  subnet_ids               = module.vpc.subnets.private[*].id
  additional_ingress_cidrs = []

  tag_map = {
    Environment = "dev"
    Project     = "pg-app"
  }
}

module "dm_neptune" {
  source = "../modules/tf-neptune"

  cluster_identifier                  = "dm-pg-cluster"
  availability_zones                  = slice(data.aws_availability_zones.available.names, 0, 3)
  instance_class                      = "db.t4g.medium"
  instance_count                      = 2
  engine_version                      = "*******"
  iam_database_authentication_enabled = false
  preferred_backup_window             = "03:00-04:00"
  skip_final_snapshot                 = true
  deletion_protection                 = false
  export_logs_to_cloudwatch           = ["audit", "slowquery"]
  log_retention_in_days               = 7

  vpc_cidr_block           = module.vpc.vpc.cidr_block
  vpc_id                   = module.vpc.vpc.id
  subnet_ids               = module.vpc.subnets.private[*].id
  additional_ingress_cidrs = []

  tag_map = {
    Environment = "dm-dev"
    Project     = "dm-pg-app"
  }
}
