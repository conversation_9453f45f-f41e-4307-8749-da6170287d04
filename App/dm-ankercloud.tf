# AnkerCloud modules for dm_ environment only

# IAM Role for AnkerCloud Lambda Functions (dm environment)
resource "aws_iam_role" "dm_ankercloud_lambda_execution_role" {
  name = "dm-ankercloud-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name           = "dm-ankercloud-lambda-execution-role"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

resource "aws_iam_role_policy_attachment" "dm_ankercloud_lambda_basic_execution" {
  role       = aws_iam_role.dm_ankercloud_lambda_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "dm_ankercloud_lambda_vpc_execution" {
  role       = aws_iam_role.dm_ankercloud_lambda_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_policy" "dm_ankercloud_opensearch_access" {
  name        = "dm-ankercloud-opensearch-access"
  description = "Policy for AnkerCloud Lambda functions to access OpenSearch Serverless"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "aoss:*"
        ]
        Resource = "*"
      }
    ]
  })

  tags = {
    Name           = "dm-ankercloud-opensearch-access"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

resource "aws_iam_role_policy_attachment" "dm_ankercloud_lambda_opensearch_access" {
  role       = aws_iam_role.dm_ankercloud_lambda_execution_role.name
  policy_arn = aws_iam_policy.dm_ankercloud_opensearch_access.arn
}

# Additional policy for S3 access (if needed for batch jobs)
resource "aws_iam_policy" "dm_ankercloud_s3_access" {
  name        = "dm-ankercloud-s3-access"
  description = "Policy for AnkerCloud services to access S3 buckets"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::ankercloud-*",
          "arn:aws:s3:::ankercloud-*/*"
        ]
      }
    ]
  })

  tags = {
    Name           = "dm-ankercloud-s3-access"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

resource "aws_iam_role_policy_attachment" "dm_ankercloud_lambda_s3_access" {
  role       = aws_iam_role.dm_ankercloud_lambda_execution_role.name
  policy_arn = aws_iam_policy.dm_ankercloud_s3_access.arn
}

# Policy for Batch job submission from Lambda
resource "aws_iam_policy" "dm_ankercloud_batch_access" {
  name        = "dm-ankercloud-batch-access"
  description = "Policy for AnkerCloud Lambda functions to submit batch jobs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "batch:SubmitJob",
          "batch:DescribeJobs",
          "batch:DescribeJobQueues",
          "batch:DescribeJobDefinitions",
          "batch:ListJobs"
        ]
        Resource = "*"
        Condition = {
          StringLike = {
            "batch:JobQueue" = "arn:aws:batch:*:*:job-queue/dm-ankercloud-*"
          }
        }
      }
    ]
  })

  tags = {
    Name           = "dm-ankercloud-batch-access"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

resource "aws_iam_role_policy_attachment" "dm_ankercloud_lambda_batch_access" {
  role       = aws_iam_role.dm_ankercloud_lambda_execution_role.name
  policy_arn = aws_iam_policy.dm_ankercloud_batch_access.arn
}

resource "aws_security_group" "dm_ankercloud_lambda_sg" {
  name        = "dm-ankercloud-lambda-sg"
  description = "Security group for AnkerCloud Lambda functions"
  vpc_id      = module.vpc.vpc.id

  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name           = "dm-ankercloud-lambda-sg"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

# AnkerCloud OpenSearch Serverless Module (dm environment)
module "dm_ankercloud_opensearch" {
  source = "../modules/ankercloud-tf-opensearch"

  name_prefix = "dm-ankr"
  environment = "dm"

  collection_name        = "dm-ankercloud-collection"
  collection_description = "AnkerCloud OpenSearch Serverless collection for dm environment"
  collection_type        = "SEARCH"

  data_access_policy_principals = [aws_iam_role.dm_ankercloud_lambda_execution_role.arn]

  network_access_type = "Public"
  resource_pattern    = "dm-ankercloud-*"

  tags = {
    Name           = "dm-ankercloud-opensearch"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

# AnkerCloud Lambda Functions Module (dm environment)
module "dm_ankercloud_lambda" {
  source = "../modules/ankercloud-tf-lambda"

  name_prefix = "dm-ankercloud"
  environment = "dm"

  lambda_search_image_uri     = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_search_function:latest"
  lambda_collection_image_uri = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_collection_creation:latest"
  lambda_execution_role_arn   = aws_iam_role.dm_ankercloud_lambda_execution_role.arn
  lambda_timeout              = 900
  lambda_memory_size          = 1024
  environment_variables = {
    OPENSEARCH_ENDPOINT = module.dm_ankercloud_opensearch.collection_endpoint
    LOG_LEVEL           = "INFO"
  }

  search_num_results = "5"

  vpc_config = {
    subnet_ids         = module.vpc.subnets.private[*].id
    security_group_ids = [aws_security_group.dm_ankercloud_lambda_sg.id]
  }

  tags = {
    Name           = "dm-ankercloud-lambda"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}

# AnkerCloud Batch Job Module (dm environment)
module "dm_ankercloud_batch" {
  source = "../modules/ankercloud-tf-batchjob"

  vpc_id                     = module.vpc.vpc.id
  subnet_ids                 = module.vpc.subnets.private[*].id
  security_group_cidr_blocks = [module.vpc.vpc.cidr_block]

  name_prefix = "dm-ankercloud"
  environment = "dm"

  batch_container_image = "652989381321.dkr.ecr.us-east-1.amazonaws.com/ankercloud_batch_jobs:latest"
  batch_instance_types  = ["c5.large", "c5.xlarge", "c5.2xlarge"]
  batch_max_vcpus       = 32
  batch_min_vcpus       = 0

  job_vcpu   = "2"
  job_memory = "4096"

  batch_job_definition_command = [
    "echo",
    "hello world"
  ]

  tags = {
    Name           = "dm-ankercloud-batch"
    BillingProject = "PerceptionGrid"
    Project        = "AnkerCloud"
    Environment    = "dm"
  }
}
