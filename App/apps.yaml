services:
  # TDOD: Need this one in here?
  # - service_name: "pg-hydra"
  - service_name: "pg-proximity"
    path: "/proximity"
    image_identifier: "652989381321.dkr.ecr.us-east-1.amazonaws.com/pg/proximity@sha256:8a826387781e9a33ab55c5341b42e14e44383669b31574c6f66e8e4a16cd6f49"
    image_port: "7000"
    health_check_protocol: "TCP"
    health_check_path: "/"
    health_check_interval: 5
    health_check_timeout: 2
    healthy_threshold: 1
    unhealthy_threshold: 5

  - service_name: "pg-graphity"
    image_identifier: "652989381321.dkr.ecr.us-east-1.amazonaws.com/pggraphityapi:638770422961608247"
    path: "/graphity"
    image_port: "8080"
    health_check_protocol: "TCP"
    health_check_path: "/"
    health_check_interval: 5
    health_check_timeout: 2
    healthy_threshold: 3
    unhealthy_threshold: 3

  - service_name: "pg-dharma"
    image_identifier: "652989381321.dkr.ecr.us-east-1.amazonaws.com/pgdharmaapi:638752363933430111"
    path: "/dharma"
    image_port: "8080"
    health_check_protocol: "TCP"
    health_check_path: "/"
    health_check_interval: 5
    health_check_timeout: 2
    healthy_threshold: 3
    unhealthy_threshold: 3
