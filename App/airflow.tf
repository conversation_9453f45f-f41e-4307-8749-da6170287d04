module "mwaa" {
  source = "../modules/tf-mwaa"

  name              = "pg-airflow"
  airflow_version   = "2.10.1"
  environment_class = "mw1.micro"

  vpc_id         = module.vpc.vpc.id
  vpc_cidr_block = module.vpc.vpc.cidr_block
  subnet_ids     = slice(module.vpc.subnets.private[*].id, 0, 2)

  min_workers = 1
  max_workers = 1

  dag_processing_log_level = "WARNING"
  scheduler_log_level      = "WARNING"
  task_log_level           = "WARNING"
  webserver_log_level      = "ERROR"
  worker_log_level         = "ERROR"

  airflow_configuration_options = {
    "webserver.dag_default_view" = "tree"
    "webserver.dag_orientation"  = "TB"
    "logging.logging_level"      = "INFO"
  }
}

module "dm_mwaa" {
  source = "../modules/tf-mwaa"

  name              = "dm-pg-airflow"
  airflow_version   = "2.10.1"
  environment_class = "mw1.micro"

  vpc_id         = module.vpc.vpc.id
  vpc_cidr_block = module.vpc.vpc.cidr_block
  subnet_ids     = slice(module.vpc.subnets.private[*].id, 0, 2)

  min_workers = 1
  max_workers = 1

  dag_processing_log_level = "WARNING"
  scheduler_log_level      = "WARNING"
  task_log_level           = "WARNING"
  webserver_log_level      = "ERROR"
  worker_log_level         = "ERROR"

  airflow_configuration_options = {
    "webserver.dag_default_view" = "tree"
    "webserver.dag_orientation"  = "TB"
    "logging.logging_level"      = "INFO"
  }
}
