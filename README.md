# tf-aws

This is a base layer, with supporting modules.

## Bootstrap

[Bootstraping](bootstrap/)

## Modules

- [tf-aurora](modules/tf-aurora/README.md)
- [tf-bastion](modules/tf-bastion/README.md)
- [tf-cloudfront](modules/tf-cloudfront/README.md)
- [tf-memorydb](modules/tf-memorydb/README.md)
- [tf-mwaa](modules/tf-mwaa/README.md)
- [tf-neptune](modules/tf-neptune/README.md)
- [tf-opensearch](modules/tf-opensearch/README.md)
- [tf-vpc](modules/tf-vpc/README.md)
