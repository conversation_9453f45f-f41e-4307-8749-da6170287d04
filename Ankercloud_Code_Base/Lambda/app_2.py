import json
import boto3
from dotenv import load_dotenv
import os
import botocore
from opensearchpy.client import OpenSearch
from opensearchpy import OpenSearch, RequestsHttpConnection, AWSV4SignerAuth
from langchain_community.vectorstores import OpenSearchVectorSearch
from requests_aws4auth import AWS4Auth
import time
import joblib
import io
load_dotenv()
print('Credentials::',load_dotenv())

bedrock = boto3.client(service_name='bedrock-runtime',region_name='us-east-1')
opensearch_client = boto3.client("opensearchserverless", region_name="us-east-1")
number_of_search=os.getenv('number_of_search',3)
client = boto3.client("opensearchserverless", region_name="us-east-1")
s3_client=boto3.client('s3')

#lambda function that runs first after getting called.
def createEncryptionPolicy(client):
    """creates an encryption policy that matches all collections beginning with pg-"""
    
    try:
        policy_json = """
        {
            "Rules": [
                {
                    "ResourceType": "collection",
                    "Resource": [
                        "collection/pg-*"
                    ]
                }
            ],
            "AWSOwnedKey": true
        }
        """
        response = client.create_security_policy(
            description='Encryption policy for pg collections',
            name='security-policy',
            policy=policy_json,
            type='encryption'
        )
        print('\nEncryption policy created:')
        print(response)
    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'ConflictException':
            print(
                '[ConflictException] The policy name or rules conflict with an existing policy.')
        else:
            raise error
#this fun create's a network policy        
def createNetworkPolicy(client):
    """creates a network policy that matches all collections beginning with pg-"""
    try:
        response = client.create_security_policy(
            description='Network policy for pg collections',
            name='pg-network-policy',
            policy="""
                [
                {
                    "Rules": [
                    {
                        "Resource": [
                        "collection/pg-*"
                        ],
                        "ResourceType": "dashboard"
                    },
                    {
                        "Resource": [
                        "collection/pg-*"
                        ],
                        "ResourceType": "collection"
                    }
                    ],
                    "AllowFromPublic": true
                }
                ]   """,
            type='network'
        )
        print('\nNetwork policy created:')
        print(response)
    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'ConflictException':
            print(
                '[ConflictException] A network policy with this name already exists.')
        else:
            raise error
#this fun can define who can access the open search collection and index's.
def createAccessPolicy(client):
    """creates a data access policy that matches all collections beginning with pg-"""
    try:
        response = client.create_access_policy(
            description='Data access policy for PG collections',
            name='pg-data-access-policy',
            policy="""
                [
  {
    "Rules": [
      {
        "Resource": [
          "collection/pg-*"
        ],
        "Permission": [
          "aoss:CreateCollectionItems",
          "aoss:DeleteCollectionItems",
          "aoss:UpdateCollectionItems",
          "aoss:DescribeCollectionItems"
        ],
        "ResourceType": "collection"
      },
      {
        "Resource": [
          "index/pg-*/*"
        ],
        "Permission": [
          "aoss:CreateIndex",
          "aoss:DeleteIndex",
          "aoss:UpdateIndex",
          "aoss:DescribeIndex",
          "aoss:ReadDocument",
          "aoss:WriteDocument"
        ],
        "ResourceType": "index"
      }
    ],
    "Principal": [
      "arn:aws:iam::652989381321:role/service-role/pg-index-query-ankercloud-dev-role-oojxmin3",
      "arn:aws:iam::652989381321:user/vishnu",
      "arn:aws:iam::652989381321:user/raffi",
      "arn:aws:iam::652989381321:role/service-role/AmazonSageMaker-ExecutionRole-20250306T150180",
      "arn:aws:iam::652989381321:role/ecsInstanceRole"
    ],
    "Description": "Rule 1"
  }
]
                """,
            type='data'
        )
        print('\nAccess policy created:')
        print(response)
    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'ConflictException':
            print(
                '[ConflictException] An access policy with this name already exists.')
        else:
            raise error
#this function create's the Opensearch collection.
def createCollection(client):
    try:
        response = client.create_collection(
            name='pg-collection-opensearch',
            type='VECTORSEARCH',
            standbyReplicas='DISABLED'
        )
        return(response)
    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'ConflictException':
            print(
                '[ConflictException] A collection with this name already exists. Try another name.')
        else:
            raise error

def find_s3_key(bucket_name, file_name):
    s3 = boto3.client("s3")
    try:
        # Search for the file in all subfolders
        paginator = s3.get_paginator("list_objects_v2")
        for page in paginator.paginate(Bucket=bucket_name):
            for obj in page.get("Contents", []):
                if obj["Key"].endswith(file_name):
                    print(f"Found tracker file: {obj['Key']}")
                    return obj["Key"]
        
        print(f"File '{file_name}' not found in any subfolder.")
        return None
    
    except botocore.exceptions.ClientError as e:
        print(f"Error searching for {file_name}: {e}")
        raise
      
#this fun create a total of 3 index for doc,image,mapping embeddings.
def create_index_opensearch(client,bucket_name='ankercloud-bucket-poc'):
    print('Inside the index creation function')
    response_key=find_s3_key( bucket_name=bucket_name,file_name='data.txt')
    print('Response key:',response_key)
    response = s3_client.get_object(
            Bucket=bucket_name,
            Key=response_key,
        )
    print('Response one.',response)
    response_4_key=find_s3_key(bucket_name=bucket_name,file_name='model_embeddings.txt')
    response_4=s3_client.get_object(
        Bucket=bucket_name,
        Key=response_4_key,
    )
    print('Response 4.',response_4)
    document_embeddings = json.load(response['Body'])
    image_embeddings = json.load(response_4['Body'])
    response=client.batch_get_collection(names=['pg-collection-opensearch'])
    host=response['collectionDetails'][0]['collectionEndpoint'].replace('https://','')
    aoss_endpoint = host
    index_name = "doc-embeddings"
    index_name_2 ="image-embeddings"
    credentials = boto3.Session().get_credentials()
    region = 'us-east-1'
    service = 'aoss'
    auth = AWS4Auth(credentials.access_key, credentials.secret_key,region, service, session_token=credentials.token)
    print('Hey there')

    aoss_client = OpenSearch(
        hosts=[{'host': aoss_endpoint, 'port': 443}],
        http_auth=auth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection,
        timeout=60
    )
    #which key/attribute you wanted to do KNN search.here it is embeddings.
    indexBody = {
        "settings": {
            "index.knn": True
        },
        "mappings": {
            "properties": {                   
                "embeddings": {               
                    "type": "knn_vector",     
                    "dimension": 1024,
                    "method": {               
                        "space_type": "l2",   
                        "engine": "faiss",    
                        "name": "hnsw"        
                    }
                }
            }
        }
    }

    try:
        print('creating the new indexes for doc,image,mapping')
        create_response = aoss_client.indices.create(index_name, body=indexBody)
        print('\nCreating index:')
        print(create_response)
        create_response_2 = aoss_client.indices.create(index_name_2, body=indexBody)
    except Exception as e:
        print(e)
    #doc embeddings are stored inside the index.
    a=0
    for split in document_embeddings:
        response = aoss_client.index(
                index=index_name,
                body={
                'metadata': split['metadata'],
                'page_content': split['page_content'].replace('\n', '').strip(),
                'embeddings': split['embeddings']
            }
            )
        a+=1
    print('\n All Documents added:',a)
    print(response)
    #image embeddings index.
    b=0
    for split in image_embeddings:
        print(split['image_path'])
        response = aoss_client.index(
                index=index_name_2,
                body={
                'file_path':split['image_path'],
                'embeddings': split['image_embedding']
            }
            )
        b+=1
    print('\n All Images with descrition added:',b)
    print(response)

#fun to call the create collection and policies that are need to associated with it.
def create_collection(client,bucket_name):
    print('Creating...')
    createEncryptionPolicy(client)
    createNetworkPolicy(client)
    createAccessPolicy(client)
    print('Done with policy creation...')
    createCollection(client)  
    waitForCollectionCreation(client,bucket_name)  
    response = client.list_collections()
    final_ans=response['collectionSummaries'][0]['id']
    return final_ans   
#fun to delete the collection and policies after use.
def delete_complete_collection(client):
    print('Deleting...')
    response = client.list_collections()
    print(response['collectionSummaries'][0]['id'])
    ans=f'Collection with the following ID:{response['collectionSummaries'][0]['id']} has been deleted'
    client.delete_collection(id=response['collectionSummaries'][0]['id'])    
    print('Collection deleted.....')
    client.delete_access_policy( type='data',name='pg-data-access-policy' )
    client.delete_security_policy(type='network',name='pg-network-policy')
    client.delete_security_policy(type='encryption',name='security-policy')
    print('All policy has been deleted...')
    final_ans=ans+"\n All policy associated with this collection is deleted"
    return final_ans

#after creating the collection it checks for the status of the collection for every 30 seconds until the Collection is active.
def waitForCollectionCreation(client,bucket_name):
    """Waits for the collection to become active"""
    response = client.batch_get_collection(
        names=['pg-collection-opensearch'])
    while (response['collectionDetails'][0]['status']) == 'CREATING':
        print('Creating collection...')
        time.sleep(30) 
        response = client.batch_get_collection(
            names=['pg-collection-opensearch'])
    print('\nCollection successfully created:')
    print(response["collectionDetails"])
    # Extract the collection endpoint from the response
    host = (response['collectionDetails'][0]['collectionEndpoint'])
    final_host = host.replace("https://", "")
    ans=create_index_opensearch(client,bucket_name)
    print('Answer index::',ans)
#the main function which is executed at the begining(entry point).
def handler(event,context):
    print('Inside the lambda function',event)
    print('Context:',context)
    body = json.loads(event['body'])
    collection_creation=body['collection_creation'] if 'collection_creation' in body else ''
    collection_deletion=body['collection_deletion'] if 'collection_deletion' in body else ''
    bucket_name=body['bucket_name'] if 'bucket_name' in body else ''
    print('Bucket name is::',bucket_name)
    print(f"Collection delete or create :{collection_creation}")
    client = boto3.client("opensearchserverless", region_name="us-east-1")
    response = client.list_collections()
    print(client)
    try:
        if collection_creation =='yes':
            ans=create_collection(client,bucket_name)
            print('Answer...collection:',ans)
            return {
            'statusCode': 200,  
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps(ans)
            }
        elif collection_deletion=='yes':
            ans=delete_complete_collection(client)
            print('Answer...Delete:',ans)
            return {
            'statusCode': 200,  
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps(ans)
            }


    except Exception as e:
        return f" Error retriving the index: {e}"
