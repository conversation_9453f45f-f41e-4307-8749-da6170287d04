import json
import boto3
from langchain_community.embeddings import BedrockEmbeddings
from dotenv import load_dotenv
import os
import botocore
from opensearchpy.client import OpenSearch
from opensearchpy import OpenSearch, RequestsHttpConnection, AWSV4SignerAuth
from langchain_community.vectorstores import OpenSearchVectorSearch
from requests_aws4auth import AWS4Auth
import time
import joblib
import io
import prompt_gallery

load_dotenv(dotenv_path=".env")
print('Credentials::',load_dotenv())

MODEL_ID = "amazon.nova-micro-v1:0"
bedrock = boto3.client(service_name='bedrock-runtime',region_name='us-east-1')
opensearch_client = boto3.client("opensearchserverless", region_name="us-east-1")
bedrock_embeddings = BedrockEmbeddings(model_id="amazon.titan-embed-image-v1",
                                       client=bedrock) 
number_of_search=os.getenv('number_of_search',3)
client = boto3.client("opensearchserverless", region_name="us-east-1")
s3_client=boto3.client('s3')

print('Number of searches that needs to be retrived::',number_of_search)

#this function retrieves the index based on the query.
def open_search_query(query,endpoint,index_name):
    aoss_endpoint=endpoint
    print('Endpoint:',endpoint)
    client = boto3.client("opensearchserverless", region_name="us-east-1")
    response = client.list_collections()
    credentials = boto3.Session().get_credentials()
    print('collection name:',response)
    region = 'us-east-1'
    service = 'aoss'
    auth = AWS4Auth(credentials.access_key, credentials.secret_key,region, service, session_token=credentials.token)

    index_name=index_name
    aoss_client = OpenSearch(
        hosts=[{'host': aoss_endpoint, 'port': 443}],
        http_auth=auth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection
    )
    query_vec=bedrock_embeddings.embed_documents([query])
    #search query with number of retrival context.
    
    q={
    "size":number_of_search,#num of index to be retrieved
    "query": {
        "knn": {
        "embeddings": {
            "vector": query_vec[0],
            "k": number_of_search
        }
        }
    },
    "timeout": "30s"
    }
    retrived_index=aoss_client.search(index=index_name,body=q,request_timeout=30)
    print(f"Retrieved context are::{retrived_index}")
    return retrived_index
#this fun after retrieving the index the model generates the response based on that context.
def chat_model(retrived_index,query):
    
    context = [hit['_source']['page_content'] for hit in retrived_index['hits']['hits']]
    print(f'The number of context retrived are {len(context)}and they are as follows:{context}')
    print(f'Query::{query}')
    messages = [
        {
            "role": "user",
            "content": [
                {"text": prompt_gallery.query_prompt.format(context=context,query=query)
                 
                 }
            ]
        }
    ]

#using the nova micro model for creating conversation.
    response = bedrock.invoke_model(
        modelId=MODEL_ID,
        body=json.dumps({
            "messages": messages,
            "inferenceConfig": {"temperature": 0.8}  
        })
    )
    response_body = json.loads(response["body"].read().decode("utf-8"))
    response_text= response_body["output"]["message"]["content"][0]["text"].splitlines()
    res=list(filter(None, response_text))

    print(res)
    return res



#the main function which is executed at the begining(entry point).
def handler(event,context):
    print('Inside the lambda function',event)
    print('Context:',context)
    body = json.loads(event['body'])
    credentials = boto3.Session().get_credentials()
    client = boto3.client("opensearchserverless", region_name="us-east-1")
    response = client.list_collections()
    print('Available Opensearch collection:',client)

    try:
        query=body['query']
        index_name=body['index_name']
        response=opensearch_client.batch_get_collection(names=['pg-collection-opensearch'])
        endpoint=response['collectionDetails'][0]['collectionEndpoint'].replace('https://','')
        if index_name=='doc-embeddings':
            print(f"query:{query},endpoint:{endpoint},index_name:{index_name}")
            res=open_search_query(query,endpoint,index_name)
            source_files = [hit['_source']['metadata']['source'] for hit in res['hits']['hits']]
            final_ans=chat_model(res,query)
            print('Final response:',final_ans)
            if source_files:   
                print('Source files are::',source_files)
                ans={'result':final_ans,
                 'source_url':source_files}
            else:
                print('No source file found...')
                ans={'result':final_ans}
        elif index_name=='image-embeddings':
            print('Image embeddings:::::')
            print(f"Query::,{query},index_name:{index_name}")
            res=open_search_query(query,endpoint,index_name)
            print('Result from open search::',res)
            final_ans=[hit['_source']['file_path'] for hit in res['hits']['hits']]
            ans={'result':final_ans}
        return {
            'statusCode': 200,  
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps(ans)
            }
    except Exception as e:
        return f" Error retriving the index: {e}"
