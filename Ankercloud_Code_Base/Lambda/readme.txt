app.py
Implements the search mechanism logic, enabling query and retrieval operations.

Dockerfile
Defines the containerization process for the search mechanism application.

app_2.py
Contains logic for creating and deleting OpenSearch collections.

Dockerfile_2
Provides the containerization setup for managing OpenSearch collection operations.

requirements.txt
Lists all necessary libraries and dependencies required to run the applications.