main.py
Serves as the container's entrypoint. It loads tracking and embedding files from Amazon S3, downloads only unprocessed data, and categorizes files based on their format (e.g., .glb, .pdf, .png).

text_processing.py
Contains the logic for processing text-based documents and generating their embeddings.

image_processing.py
Handles the processing of moodboard descriptions and related image data.

models_3d_processing.py
Responsible for generating three different views of 3D models and creating their embeddings.

prompt_gallery.py
Defines the prompt templates used during image processing tasks.

open_search.py
Implements functionality for inserting embeddings into an existing OpenSearch collection, provided the collection is in an ACTIVE state.

dependencies.txt
Lists all required libraries and packages for the application.
