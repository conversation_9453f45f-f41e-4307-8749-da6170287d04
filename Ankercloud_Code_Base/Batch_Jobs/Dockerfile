FROM python:3.12-slim

RUN apt-get update && apt-get install -y \
    xvfb \
    libegl1 \
    libegl1-mesa \
    libgles2-mesa \
    libgl1-mesa-glx \
    libosmesa6 \
    libxi6 \
    libxrandr2 \
    libxxf86vm1 \
    libxcursor1 \
    libxinerama1 \
    libxmu6 \
    python3-dev \
    build-essential \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*
COPY dependencies.txt .
RUN pip install -r dependencies.txt 
RUN pip install pyvirtualdisplay
COPY open_search_utils.py .
COPY processing_tracker.py .
COPY models_3D_processing.py .
COPY prompt_gallery.py .
COPY s3_utils.py .
COPY text_file_processing.py .
COPY main.py . 
COPY image_processing.py .
RUN pip install --upgrade pip 
ENTRYPOINT ["python", "main.py"]
