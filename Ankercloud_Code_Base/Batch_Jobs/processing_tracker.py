import os
import json
from urllib.parse import urljoin
from dotenv import load_dotenv
load_dotenv()
def load_processed_tracker(filepath="outputs/processed_files.json"):
    """
    Load a tracker file that stores processed file metadata.

    Returns:
        dict: {s3_uri: metadata}
    """
    if os.path.exists(filepath):
        with open(filepath, "r", encoding="utf-8") as f:
            return json.load(f)
    return {}

def save_processed_tracker(tracker, filepath="outputs/processed_files.json"):
    """
    Save tracker data back to disk.

    Args:
        tracker (dict): Processed file metadata
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(tracker, f, indent=2)

def filter_unprocessed_files(categorized_files: dict, bucket: str, tracker: dict, valid_status: str = "done") -> dict:
    """
    Filters out already processed S3 files from each file category.

    Args:
        categorized_files (dict): Output of list_and_categorize_s3_files()
        bucket (str): The S3 bucket name
        tracker (dict): Dict of already processed files
        valid_status (str): Status to check for (e.g., 'done')

    Returns:
        dict: Same structure as input but with only unprocessed files
    """
    filtered = {}

    for category, file_keys in categorized_files.items():
        filtered[category] = [
            key for key in file_keys
            if f"s3://{bucket}/{key}" not in tracker or tracker[f"s3://{bucket}/{key}"].get("status") != valid_status
        ]

    return filtered
