from opensearchpy import OpenSearch, RequestsHttpConnection, AWSV4SignerAuth
from langchain_community.vectorstores import OpenSearchVectorSearch
from requests_aws4auth import AWS4Auth
import boto3
client = boto3.client("opensearchserverless", region_name="us-east-1")

def store_embedding_in_index_opensearch(index_name,data):
    response=client.batch_get_collection(names=['pg-collection-opensearch'])
    host=response['collectionDetails'][0]['collectionEndpoint'].replace('https://','')
    aoss_endpoint = host
    index_name_1 = "doc-embeddings"
    index_name_2 ="image-embeddings"
    credentials = boto3.Session().get_credentials()
    region = 'us-east-1'
    service = 'aoss'
    auth = AWS4Auth(credentials.access_key, credentials.secret_key,region, service, session_token=credentials.token)


    aoss_client = OpenSearch(
        hosts=[{'host': aoss_endpoint, 'port': 443}],
        http_auth=auth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection,
        timeout=60
    )#which key/attribute you wanted to do KNN search.here it is embeddings.
    indexBody = {
        "settings": {
            "index.knn": True
        },
        "mappings": {
            "properties": {                   
                "embeddings": {               
                    "type": "knn_vector",     
                    "dimension": 1024,
                    "method": {               
                        "space_type": "l2",   
                        "engine": "faiss",    
                        "name": "hnsw"        
                    }
                }
            }
        }
    }
    indices = aoss_client.indices.get('*')
    print(indices.keys())           
    if index_name==index_name_1:
        for split in data:
            response = aoss_client.index(
                    index=index_name_1,
                    body={
                    'metadata': split['metadata'],
                    'page_content': split['page_content'].replace('\n', '').strip(),
                    'embeddings': split['embeddings']
                }
                )
        print('Completed adding the new documents:',response)
    elif index_name==index_name_2:
        for split in data:
            print(split['image_path'])
            response = aoss_client.index(
                    index=index_name_2,
                    body={
                    'file_path':split['image_path'],
                    'embeddings': split['image_embedding']
                }
                )
        print('Completed adding the new 3d models:',response)
    else:
        print(f'Sorry no index matching found provided index name {index_name} and available index are:{indices.keys()}')