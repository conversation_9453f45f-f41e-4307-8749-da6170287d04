import boto3, os
import re, logging
from collections import defaultdict
from dotenv import load_dotenv
load_dotenv()
def list_and_categorize_s3_files(s3_uri):
    """
    Lists all files in an S3 URI recursively and categorizes them by file extension.
    
    Args:
        s3_uri (str): S3 URI like 's3://bucket-name/folder/'
        
    Returns:
        dict: Dictionary with file type categories as keys and lists of S3 keys as values
    """
    # Parse bucket and prefix
    match = re.match(r's3://([^/]+)/?(.*)', s3_uri)
    if not match:
        raise ValueError("Invalid S3 URI format. Use s3://bucket-name/ or s3://bucket-name/prefix/")
    
    bucket, prefix = match.groups()
    s3 = boto3.client('s3')
    paginator = s3.get_paginator('list_objects_v2')

    categorized_files = defaultdict(list)

    for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
        if 'Contents' in page:
            for obj in page['Contents']:
                key = obj['Key']
                key_lower = key.lower()

                if key_lower.endswith('.glb'):
                    categorized_files['glb'].append(key)
                elif key_lower.endswith(('.png', '.jpg', '.jpeg')):
                    categorized_files['images'].append(key)
                elif key_lower.endswith(('.pdf','.docx')):
                    categorized_files['documents'].append(key)
                elif key_lower.endswith('.mp4'):
                    categorized_files['videos'].append(key)
                elif key_lower.endswith(".json"):
                    categorized_files['json'].append(key)
                else:
                    categorized_files['others'].append(key)
    
    return dict(categorized_files)

def download_s3_files(s3_keys, bucket_name, local_root="tmp"):
    """
    Downloads files from S3 to a local directory, preserving folder structure.
    
    Args:
        s3_keys (list of str): List of S3 keys (object paths within the bucket)
        bucket_name (str): S3 bucket name
        local_root (str): Root local directory to download into

    Returns:
        list of str: List of local file paths
    """
    os.makedirs(local_root, exist_ok=True)
    s3 = boto3.client('s3')
    local_paths = []

    for s3_key in s3_keys:
        local_path = os.path.join(local_root, s3_key)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        s3.download_file(bucket_name, s3_key, local_path)
        local_paths.append(local_path)

    return local_paths


def upload_file_to_s3(local_path, bucket_name, s3_key):
    """
    Uploads a single local file to the specified S3 location.

    Args:
        local_path (str): Local file path.
        bucket_name (str): Target S3 bucket name.
        s3_key (str): Target S3 key (path inside the bucket).
    """
    s3_client = boto3.client('s3')
    try:
        if os.path.isfile(local_path):
            s3_client.upload_file(local_path, bucket_name, s3_key)
            logging.info(f" Uploaded file {local_path} to s3://{bucket_name}/{s3_key}")
        elif os.path.isdir(local_path):
            for root, dirs, files in os.walk(local_path):
                for file in files:
                    full_path = os.path.join(root, file)
                    relative_path = os.path.relpath(full_path, start=local_path).replace("\\", "/")
                    s3_full_key = os.path.join(s3_key, relative_path).replace("\\", "/")
                    s3_client.upload_file(full_path, bucket_name, s3_full_key)
                    logging.info(f"Uploaded file {full_path} to s3://{bucket_name}/{s3_full_key}")
        else:
            raise ValueError(f"The path {local_path} is neither a file nor a directory.")
    except Exception as e:
        logging.error(f"Failed to upload {local_path} to s3://{bucket_name}/{s3_key}: {e}", exc_info=True)
        raise e