import prompt_gallery
import boto3, json, base64, time, logging,io
from dotenv import load_dotenv
load_dotenv()

BEDROCK_CLIENT = boto3.client(
    service_name="bedrock-runtime",
    region_name="us-east-1",
    )
IMAGE_DESCRIPTION_MODEL_ID = 'us.anthropic.claude-3-7-sonnet-20250219-v1:0'


def generate_image_description(image_path:str):
    """
    Reads a local image file and sends it to Bedrock for description generation.

    Args:
        image_path (str): Local path to a .png image

    Returns:
        str: Descriptive text returned by the model
    """
    # Read and encode image
    with open(image_path, "rb") as img_file:
        base64_string = base64.b64encode(img_file.read()).decode("utf-8")

    # Construct prompts
    system_instruction = prompt_gallery.moodboard_description_prompt
    native_request = {
        "anthropic_version": "bedrock-2023-05-31", 
        "system":system_instruction,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": "image/png",
                            "data": base64_string
                        }
                    },
                    {
                        "type": "text",
                        "text": "Do an image analysis. Provide detailed descriptive information about the image."
                    }
                ]
            }
        ],
        "max_tokens": 4096
    }

    # Call Bedrock
    response = BEDROCK_CLIENT.invoke_model(
        modelId=IMAGE_DESCRIPTION_MODEL_ID,
        body=json.dumps(native_request)
    )
    model_response = json.loads(response["body"].read())
    content_text = model_response["content"][0]["text"]

    print("Model response:", content_text)
    time.sleep(5)  # optional throttle

    return content_text

def generate_3D_model_description(model_view,file_name ,context: list):
    """
    Generate a description for a 3D model view (provided as a PIL image) using Claude-3 and contextual character data.

    Args:
        model_view (PIL.Image.Image): Rendered image of a 3D model (as a PIL Image).
        file_name (str): File name of the 3D model
        context (list): List of reference character descriptions (strings).

    Returns:
        dict: {'description': generated_text} on success,
              {'description': 'No Description Found'} on failure.
    """

    # Convert the PIL image to a base64-encoded PNG string
    buffer = io.BytesIO()
    model_view.save(buffer, format="PNG")
    image_bytes = buffer.getvalue()
    base64_image = base64.b64encode(image_bytes).decode("utf-8")

    try:
        # Convert context list to a newline-separated string
        context_str = "\n".join(context) if isinstance(context, list) else str(context)

        # Format the system prompt with the provided context
        system_instructions = prompt_gallery.description_matching_prompt.format(context=context_str,image_path = file_name)

        # Construct the request payload for Claude 3 model (via Bedrock)
        native_request = {
            "anthropic_version": "bedrock-2023-05-31",
            "system": system_instructions,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/png",
                                "data": base64_image
                            }
                        },
                        {
                            "type": "text",
                            "text": "Do an image analysis and identify the character in the image using the provided context."
                        }
                    ]
                }
            ],
            "max_tokens": 4096
        }

        # Send the request to Bedrock
        response = BEDROCK_CLIENT.invoke_model(
            modelId='us.anthropic.claude-3-7-sonnet-20250219-v1:0',
            body=json.dumps(native_request)
        )

        # Parse the model's response
        model_response = json.loads(response["body"].read())
        content_text = model_response["content"][0]["text"]
        time.sleep(5)
        return {'description': content_text}

    except Exception as e:
        logging.error(f"Error generating 3D model description: {e}", exc_info=True)
        return {'description': 'No Description Found'}



