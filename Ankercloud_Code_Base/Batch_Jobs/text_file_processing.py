from langchain.embeddings import BedrockEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import fitz, os, boto3, time


BEDROCK_MODEL_ID = "amazon.titan-embed-image-v1"
REGION_NAME = 'us-east-1'

bedrock = boto3.client(service_name='bedrock-runtime',region_name=REGION_NAME)
bedrock_embeddings = BedrockEmbeddings(model_id=BEDROCK_MODEL_ID,
                                       client=bedrock)

def preprocessing_text_files(text_files, bucket_name, local_root="tmp"):
    pages = []
    for text_filepath in text_files:
        relative_path = os.path.relpath(text_filepath, start=local_root).replace("\\", "/")
        s3_uri = f"s3://{bucket_name}/{relative_path}"
        # filename_only = os.path.basename(text_filepath)
        doc = fitz.open(text_filepath)
        for page_num, page in enumerate(doc):
            # print(page_num,page)
            metadata = {
                "source": s3_uri,
                "total_pages": len(doc),
                "page": page_num,
                "page_label": str(page_num + 1),
            }
            pages.append({"metadata": metadata, "page_content": page.get_text("text").strip()})

    documents = [Document(metadata=doc["metadata"], page_content=doc["page_content"]) for doc in pages]

    max_seq_len = max(len(doc.page_content) for doc in documents if doc.page_content.strip())
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=max_seq_len,
        chunk_overlap=150,
        separators=["\n\n", "\n", " "]
    )
    document_chunks = text_splitter.split_documents(documents)
    return document_chunks

def create_text_embeddings_bedrock_from_files(text_files, bucket_name, local_root="tmp"):
    document_chunks = preprocessing_text_files(text_files, bucket_name, local_root="tmp")
    document_embeddings = []
    unique_sources = set()
    for chunk in document_chunks:
        page_content = chunk.page_content.strip()
        if not page_content:
            continue
        embedding = bedrock_embeddings.embed_documents([page_content])
        document_embeddings.append({
            "metadata": chunk.metadata,
            "page_content": page_content,
            "embeddings": embedding[0],
        }) 
        unique_sources.add(chunk.metadata["source"])
        time.sleep(5)
    with open('full_track_list.txt', 'a') as f:
        for source in unique_sources:
            f.write(source + "\n")
    print('Added the doc files to track list.')
    return document_embeddings