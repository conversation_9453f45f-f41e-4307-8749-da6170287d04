import os
os.environ['PYOPENGL_PLATFORM'] = 'egl'
from pyvirtualdisplay import Display
display = Display(visible=0, size=(800, 600))
display.start()
import argparse, json
import logging
import botocore
from dotenv import load_dotenv
from urllib.parse import urlparse
from s3_utils import list_and_categorize_s3_files, download_s3_files, upload_file_to_s3
from models_3D_processing import create_glb_embeddings_bedrock_from_files
from image_processing import generate_image_description
from text_file_processing import create_text_embeddings_bedrock_from_files
from processing_tracker import load_processed_tracker, save_processed_tracker, filter_unprocessed_files
from datetime import datetime   
import boto3
from open_search_utils import store_embedding_in_index_opensearch
client = boto3.client("opensearchserverless", region_name="us-east-1")
response = client.list_collections()
load_dotenv()
s3 = boto3.client("s3")

SUPPORTED_EXTENSIONS = ('.glb', '.pdf', '.docx', '.jpg', '.jpeg', '.png')

def parse_s3_uri(s3_uri):
    parsed = urlparse(s3_uri)
    if parsed.scheme != "s3":
        raise ValueError(f"Invalid S3 URI: {s3_uri}")
    return parsed.netloc, parsed.path.lstrip("/")

def list_matching_s3_keys(bucket_name, prefix="", extensions=SUPPORTED_EXTENSIONS):
    s3 = boto3.client("s3")
    paginator = s3.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=bucket_name, Prefix=prefix)
    matching_keys = []

    for page in page_iterator:
        for obj in page.get("Contents", []):
            key = obj["Key"]
            if key.lower().endswith(extensions):
                matching_keys.append(key)

    return matching_keys

def download_s3_files(s3_keys, bucket_name, local_root="tmp"):
    print('S3 URL:', s3_keys)
    local_paths = []

    for s3_key in s3_keys:
        # Preserve the full directory structure
        local_path = os.path.join(local_root, s3_key.replace("/", os.sep))
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # Download the file
        s3.download_file(bucket_name, s3_key, local_path)
        local_paths.append(local_path)

    print('Local paths:', local_paths)
    return local_paths

def setup_logging(log_file_path):
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    if logger.hasHandlers():
        logger.handlers.clear()
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
    logger.addHandler(console_handler)
    file_handler = logging.FileHandler(log_file_path, mode='w')
    file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
    logger.addHandler(file_handler)
    return logger


def s3_upload(file_name, object_name, bucket_name):
    s3.upload_file(file_name, bucket_name, object_name)
    print(f"Uploaded {file_name} to s3://{bucket_name}/{object_name}")
    return "Done with uploading"

def download_tracker_file(bucket_name, filename="full_track_list.txt"):
    s3 = boto3.client("s3")
    try:
        # Search for the file in all subfolders
        response = s3.list_objects_v2(Bucket=bucket_name, Prefix="")
        for obj in response.get("Contents", []):
            if obj["Key"].endswith(filename):
                local_file_path = filename  # Save with the same filename locally
                s3.download_file(bucket_name, obj["Key"], local_file_path)
                print(f"Downloaded tracker file: {local_file_path}")
                return local_file_path
        
        print(f"File '{filename}' not found in any subfolder.")
    except botocore.exceptions.ClientError as e:
        print(f"Error downloading {filename}: {e}")
        raise

def get_already_processed_from_tracker(tracker_file="full_track_list.txt"):
    processed = set()
    if os.path.exists(tracker_file):
        with open(tracker_file, "r") as f:
            processed = set(line.strip() for line in f if line.strip())
    return processed

def list_and_categorize_s3_files(s3_uri):
    bucket_name, prefix = parse_s3_uri(s3_uri)
    matching_keys = list_matching_s3_keys(bucket_name, prefix)
    
    categories = {
        "glb": [],
        "images": [],
        "documents": [],
        "txt": []
    }
    
    for key in matching_keys:
        lower_key = key.lower()
        if lower_key.endswith('.glb'):
            categories["glb"].append(key)
        elif lower_key.endswith(('.jpg', '.jpeg', '.png')):
            categories["images"].append(key)
        elif lower_key.endswith(('.pdf', '.docx')):
            categories["documents"].append(key)
        elif lower_key.endswith('.txt'):
            categories["txt"].append(key)
    
    return categories

def filter_unprocessed_files(categorized_files, bucket_name, processed_tracker):
    filtered_files = {}
    
    for category, files in categorized_files.items():
        filtered_files[category] = [
            file for file in files 
            if f"s3://{bucket_name}/{file}" not in processed_tracker
        ]
    
    return filtered_files

def load_processed_tracker(tracker_path):
    processed = {}
    if os.path.exists(tracker_path):
        with open(tracker_path, "r") as f:
            for line in f:
                if line.strip():
                    processed[line.strip()] = True
    return processed

class FileCategory:
    def __init__(self, name, label, s3_keys):
        self.name = name
        self.label = label
        self.s3_keys = s3_keys
        self.local_paths = []

    def download(self, bucket_name, root="tmp"):
        if self.s3_keys:
            logging.info(f"Downloading {self.label} files...\n")
            self.local_paths = download_s3_files(self.s3_keys, bucket_name, local_root=root)

    def log_summary(self, logger):
        logger.info(f"{self.name.upper()} FILES FOUND: {len(self.s3_keys)}")
        if self.name in ['glb', 'images', 'documents'] and self.s3_keys:
            logger.info(f"\n {self.label} --")
            for f in self.s3_keys:
                logger.info(f"   - {f}")

def s3_uri_to_log_filename(s3_uri):
    bucket, path = parse_s3_uri(s3_uri)
    safe_path = path.replace('/', '_') if path else "root"
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"s3_file_log_{bucket}_{safe_path}_{timestamp}.txt"

def main(s3_uri):
    bucket_name, prefix = parse_s3_uri(s3_uri)
    log_file_name = download_tracker_file(bucket_name)
    doc_embedding_file=download_tracker_file(bucket_name,'data.txt')
    glb_embedding_file=download_tracker_file(bucket_name, 'model_embeddings.txt')
    descriptions_file=download_tracker_file(bucket_name,'moodboard_description.jsonl')


        
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process and categorize files from S3")
    parser.add_argument("--s3_uri", required=True, help="S3 URI (e.g., s3://bucket/path/)")
    args = parser.parse_args()
    print('Arguments are::',args.s3_uri)
    main(args.s3_uri)
    BUCKET, PREFIX = parse_s3_uri(args.s3_uri)
    log_file_name = s3_uri_to_log_filename(args.s3_uri)
    logger = setup_logging(log_file_name)
    logger.info(f" Log file created at: {log_file_name}")
    logger.info("=" * 60)
    logger.info(f"Starting S3 file categorization for: {args.s3_uri}")
    logger.info("=" * 60)

    #Fetching Files from s3 uri
    try:
        # Get already processed files
        categorized_files = list_and_categorize_s3_files(args.s3_uri)
        print('Categorized files:',categorized_files)
        processed_tracker = get_already_processed_from_tracker()
        print('Processed files:',processed_tracker)
        # Filter unprocessed files
        categorized_files = filter_unprocessed_files(categorized_files, BUCKET, processed_tracker) 

        # Create category objects
        categories = [
            FileCategory("glb", "GLB Files Found", categorized_files.get("glb", [])),
            FileCategory("images", "Image Files Found", categorized_files.get("images", [])),
            FileCategory("documents", "Document Files Found", categorized_files.get("documents", [])),
        ]

        print('Files that are about to be processed........',categories)
        for cat in categories:
            cat.log_summary(logger)

        logger.info("\n S3 file processing completed.")
    

    except Exception as e:
        logger.error(f" Error processing S3 URI: {e}", exc_info=True)

    #Downloading Files
    try:
        logger.info("=" * 60)
        logger.info("Starting to download relevant files\n")
        logger.info("=" * 60)
        for cat in categories:
            cat.download(BUCKET)
    except Exception as e:
        logger.error(f" Error in downloading some files: {e}", exc_info=True)

    # Processing PNG/JPEG Files (Category: images)
    try:
        IMAGE_DESCRIPTIONS = []
        print('Processing images...')
        
        if categories[1].local_paths:
            for local_path, s3_key in zip(categories[1].local_paths, categories[1].s3_keys):
                print('S3 keys are::',s3_key)
                filename = os.path.basename(s3_key)
                try:
                    logger.info(f"Processing image: {filename}")
                    
                    # Generate the description
                    description = generate_image_description(local_path)
                    
                    # Append both the S3 URI and the description
                    IMAGE_DESCRIPTIONS.append({
                        's3_uri': f"s3://{BUCKET}/{s3_key}",
                        'local_path': local_path,
                        'description': description
                    })
                    with open('full_track_list.txt', 'a') as f:
                        f.write(f"s3://{BUCKET}/{s3_key}" + "\n")
                    logger.info(f"Description generated for {filename}")
                except Exception as e:
                    logger.error(f"Failed to describe {filename}: {e}", exc_info=True)
    except Exception as e:
        logger.error(f"Error in generating descriptions for image files: {e}", exc_info=True)



    #Processing 3D Files
    try:
        if len(IMAGE_DESCRIPTIONS)>0:
            with open('moodboard_description.jsonl', "a", encoding="utf-8") as f:
                json.dump(IMAGE_DESCRIPTIONS,f,indent=4)
        embeddings_3d_models = None
        embeddings_text_docs = None
        print('Processing of 3d models........')
        with open('moodboard_description.jsonl','r') as f:
            final_moodboard_res=f.read()
        print('Final mood board description:', final_moodboard_res)
        IMAGE_DESCRIPTIONS_LIST = final_moodboard_res
        if categories[0].local_paths:
            embeddings_3d_models = create_glb_embeddings_bedrock_from_files(glb_files = categories[0].local_paths, 
                                                 bucket_name = BUCKET, num_views=3, 
                                                 save_rendered_images=True, segment=False,
                                                 character_descriptions=IMAGE_DESCRIPTIONS_LIST)
            # print(embeddings_3d_models)
            # print(model_desc)
    except Exception as e:
        logger.error(f" Error in creating embeddings some files: {e}", exc_info=True)

    #Processing Text Files
    try:
        print('Processing of the documents...')
        if categories[2].local_paths:
            embeddings_text_docs = create_text_embeddings_bedrock_from_files(text_files = categories[2].local_paths, 
                                                                             bucket_name = BUCKET)
    except Exception as e:
        logger.error(f" Error in creating embeddings some files: {e}", exc_info=True)
    

    #Saving Embeddings and Updating Tracker
    try:           
        glb_embedding_file = "model_embeddings.txt"
        doc_embedding_file = "data.txt"
        descriptions_file ="moodboard_description.jsonl"
        if embeddings_3d_models:
            with open(glb_embedding_file, "a", encoding="utf-8") as f:
                json.dump(embeddings_3d_models,f,indent=4)
            print(f"The status of the collection is:{response['collectionSummaries']}")
            active_collections = [i for i in response['collectionSummaries'] if i['status'] == 'ACTIVE']
            if active_collections:
                model_emb = store_embedding_in_index_opensearch(index_name='image-embeddings', data=embeddings_3d_models)
                print("Done, adding new 3D model embeddings to the collection")
            
            logger.info("3D model embeddings saved as TXT.")
        if embeddings_text_docs:
            with open(doc_embedding_file, 'a') as f:
                json.dump(embeddings_text_docs, f, indent=4) 
            print(f"The status of the collection is:{response['collectionSummaries']}")
            active_collections = [i for i in response['collectionSummaries'] if i['status'] == 'ACTIVE']
            if active_collections:
                doc_emb = store_embedding_in_index_opensearch(index_name='doc-embeddings', data=embeddings_text_docs)
                print("Done, adding new 3D model embeddings to the collection")
            
            logger.info("3D model embeddings saved as TXT.")
        if IMAGE_DESCRIPTIONS:
            with open(descriptions_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(IMAGE_DESCRIPTIONS_LIST)+'\n')
            logger.info(f"Image descriptions saved to: {descriptions_file}")

        # Save tracker to disk
        upload_file_to_s3(local_path = 'moodboard_description.jsonl', bucket_name=BUCKET, s3_key='outputs/moodboard_description.jsonl')
        upload_file_to_s3(local_path = log_file_name, bucket_name=BUCKET, s3_key=f'logs/{log_file_name}')
        upload_file_to_s3(local_path = 'model_embeddings.txt', bucket_name=BUCKET, s3_key='outputs/model_embeddings.txt')
        upload_file_to_s3(local_path = 'data.txt', bucket_name=BUCKET, s3_key='outputs/data.txt')
        upload_file_to_s3(local_path = 'full_track_list.txt', bucket_name=BUCKET, s3_key='outputs/full_track_list.txt')

    except Exception as e:
        logger.error(f"Failed to save output files: {e}", exc_info=True)




