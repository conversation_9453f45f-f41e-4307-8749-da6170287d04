import os
os.environ['PYOPENGL_PLATFORM'] = 'egl'
from pyvirtualdisplay import Display
display = Display(visible=0, size=(800, 600))
display.start()
from PIL import Image
import numpy as np
import pyrender
import trimesh
import boto3
import json
import base64
import io
from rembg import remove
import logging, time
from image_processing import generate_3D_model_description
from dotenv import load_dotenv

load_dotenv()
BEDROCK_MODEL_ID = "amazon.titan-embed-image-v1"
REGION_NAME = 'us-east-1'

bedrock = boto3.client(service_name='bedrock-runtime',region_name=REGION_NAME)

def create_image_embedding(image, description: str):
    """
    Generates an embedding from an image (PIL) and a description using a Bedrock multimodal model.

    Args:
        image (PIL.Image.Image): The image to be encoded.
        description (str): Textual description to pair with the image.

    Returns:
        list or None: The embedding vector, or None if the request failed.
    """
    if not isinstance(image, Image.Image):
        raise ValueError("Image input must be a PIL Image object")

    # Convert image to base64 PNG
    buffer = io.BytesIO()
    image.save(buffer, format="PNG")
    image_bytes = buffer.getvalue()
    base64_image = base64.b64encode(image_bytes).decode("utf-8")

    # Prepare request payload
    request_body = json.dumps({
        "inputText": description,
        "inputImage": base64_image,
        "embeddingConfig": {
            "outputEmbeddingLength": 1024
        }
    })

    try:
        # Call Bedrock
        bedrock_response = bedrock.invoke_model(
            body=request_body,
            modelId=BEDROCK_MODEL_ID,
            accept="application/json",
            contentType="application/json"
        )

        final_response = json.loads(bedrock_response.get("body").read())
        # print(final_response)
        # Check for errors
        if final_response.get('message') is not None:
            logging.error(f"Bedrock Invoke Model Failed: {final_response['message']}")
            return None
        
        time.sleep(5)  # optional throttle

        return final_response.get("embedding")

    except Exception as e:
        logging.error(f"Exception during Bedrock embedding generation: {e}", exc_info=True)
        return None


def create_glb_embeddings_bedrock_from_files(glb_files, bucket_name, local_root="tmp", num_views=3, save_rendered_images=True, segment=False, character_descriptions=None):
    """
    Processes a given list of GLB file paths, renders views, creates embeddings using Bedrock Titan model.

    Args:
        glb_files (list of str): List of local GLB file paths.
        bucket_name (str): Name of the S3 bucket (used to reconstruct S3 URI).
        local_root (str): Root directory where files are downloaded.
        num_views (int): Number of viewpoints per model.
        save_rendered_images (bool): Whether to save rendered images.
        segment (bool): Whether to apply background removal to images.
        character_descriptions (list): List of identified character descriptions from Images
    Returns:
        dict: Mapping from S3 URI to aggregated embedding vector.
    """
    glb_embeddings = []
    unique_sources = set()
    model_descriptions = []
    output_render_dir = "rendered_images_segmented" if segment else "rendered_images"
    if save_rendered_images:
        os.makedirs(output_render_dir, exist_ok=True)

    for glb_filepath in glb_files:
        relative_path = os.path.relpath(glb_filepath, start=local_root).replace("\\", "/")
        s3_uri = f"s3://{bucket_name}/{relative_path}"
        filename_only = os.path.basename(glb_filepath)

        logging.info(f"Processing 3D model file: {s3_uri}")

        try:
            rendered_images = render_model_views(glb_filepath, num_views=num_views, image_size=(2048, 2048))
        except Exception as e:
            logging.error(f"Failed to render views for {s3_uri}: {str(e)}", exc_info=True)
            continue

        if segment and rendered_images:
            rendered_images = [remove(img) for img in rendered_images]

        if save_rendered_images:
            model_name = filename_only.replace(".glb", "")
            model_render_dir = os.path.join(output_render_dir, model_name)
            os.makedirs(model_render_dir, exist_ok=True)
            for i, img in enumerate(rendered_images):
                image_filename = os.path.join(model_render_dir, f"view_{i}.png")
                img.save(image_filename)
                logging.info(f"Saved rendered view {i+1} to: {image_filename}")

        try:
            # Generate model-level description using one of the rendered views
            description = generate_3D_model_description(model_view = rendered_images[1], context=character_descriptions, file_name=model_name)
            logging.info(f"Description for {s3_uri}: {description.get('description')}")
        except Exception as e:
            logging.error(f"Failed to generate model description for {s3_uri}: {str(e)}", exc_info=True)
            # model_descriptions[s3_uri] = {'description': 'No Description Found'}
            continue
        try:
            image_embeddings = [create_image_embedding(rendered_images[1], description.get('description'))]
            if image_embeddings:
                glb_embeddings.append({
                    'image_path':s3_uri,
                    'image_embedding':image_embeddings[0]
                })
                if s3_uri not in unique_sources:
                    with open('full_track_list.txt', 'a') as f:
                        f.write(s3_uri + "\n")
                        print('Added the file to track list:', s3_uri)
                    unique_sources.add(s3_uri)
            else:
                logging.warning(f"Embedding extraction failed for {s3_uri}")
        except Exception as e:
            logging.error(f"Error during embedding creation for {s3_uri}: {str(e)}", exc_info=True)

    return glb_embeddings



def render_model_views(glb_filepath, num_views=3, image_size=(224, 224)):
    """
    Renders a 3D model from multiple viewpoints.

    Fixed issues:
    - Ensures camera always points at the model center regardless of viewpoint
    - Properly orbits camera around the model in a circle
    - Moves the main light with the camera to maintain consistent lighting
    - Computes a suitable camera distance based on the model's size
    - Centers the model
    """
    # Load the mesh and compute its center and scale
    mesh = trimesh.load_mesh(glb_filepath)
    center = mesh.bounding_box.centroid
    scale = mesh.bounding_box.extents.max()

    # Translate the mesh to center it at the origin
    mesh.apply_translation(-center)

    # Set an appropriate camera distance (adjust the factor as needed)
    camera_distance = scale * 2.5  # a multiplier that can be tuned

    # Create a scene and add the mesh
    scene = pyrender.Scene()
    mesh_node = scene.add(pyrender.Mesh.from_trimesh(mesh, smooth=False))

    # Setup the camera with custom near/far parameters based on model size
    camera = pyrender.PerspectiveCamera(yfov=np.pi / 3.0, aspectRatio=1.0, znear=0.1, zfar=camera_distance * 4)

    # Initial camera pose (positioned along the z-axis)
    initial_pose = np.eye(4)
    initial_pose[2, 3] = camera_distance
    camera_node = scene.add(camera, pose=initial_pose)

    # Add a directional light at the camera's position - store the node reference
    light = pyrender.DirectionalLight(color=np.ones(3), intensity=2.0)
    main_light_node = scene.add(light, pose=initial_pose)

    # Add an additional static light from a different angle to reduce harsh shadows
    extra_light_pose = np.array([
        [1, 0, 0, camera_distance/2],
        [0, 1, 0, camera_distance/2],
        [0, 0, 1, camera_distance/2],
        [0, 0, 0, 1]
    ])
    light2 = pyrender.DirectionalLight(color=np.ones(3), intensity=1.0)
    scene.add(light2, pose=extra_light_pose)

    # Prepare the offscreen renderer
    renderer = pyrender.OffscreenRenderer(image_size[0], image_size[1])
    images = []

    for i in range(num_views):
        angle = 2 * np.pi * i / num_views

        # Calculate position on a circle around the y-axis
        x = camera_distance * np.sin(angle)
        z = camera_distance * np.cos(angle)

        # Create a camera pose at this position
        camera_pose = np.eye(4)
        camera_pose[0, 3] = x
        camera_pose[2, 3] = z

        # Create a rotation matrix that makes the camera look at the origin (0,0,0)
        # First, compute the direction vector from camera to origin
        direction = -np.array([x, 0, z])  # Negative of camera position
        direction = direction / np.linalg.norm(direction)  # Normalize

        # Compute right vector assuming world up is [0,1,0]
        world_up = np.array([0, 1, 0])
        right = np.cross(direction, world_up)
        right = right / np.linalg.norm(right)

        # Compute camera up vector
        up = np.cross(right, direction)

        # Set rotation part of camera matrix
        # Camera forward direction is -z, so we use -direction
        camera_pose[:3, 0] = right
        camera_pose[:3, 1] = up
        camera_pose[:3, 2] = -direction

        # Update the camera node pose
        scene.set_pose(camera_node, camera_pose)

        # Move the main light to the new camera position
        scene.set_pose(main_light_node, camera_pose)

        # Render the scene
        color, depth = renderer.render(scene)
        image = Image.fromarray(color).convert('RGB')
        images.append(image)

    renderer.delete()  # Clean up renderer resources
    return images

